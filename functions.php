<?php
/**
 * ESP Website Theme Two Functions
 * 
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function esp_theme_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
        'style',
        'script'
    ));
    
    // Add theme support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add theme support for editor styles
    add_theme_support('editor-styles');
    
    // Add theme support for wide alignment
    add_theme_support('align-wide');
    
    // Add theme support for custom background
    add_theme_support('custom-background', array(
        'default-color' => 'FFF8E7',
        'default-image' => '',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'esp-website-theme-two'),
        'footer' => __('Footer Menu', 'esp-website-theme-two'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'esp_theme_setup');

/**
 * Enqueue Scripts and Styles
 */
function esp_theme_scripts() {
    // Enqueue Google Fonts
    wp_enqueue_style('esp-google-fonts', 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap', array(), '1.0.0');
    
    // Enqueue Material Icons
    wp_enqueue_style('esp-material-icons', 'https://fonts.googleapis.com/icon?family=Material+Icons', array(), '1.0.0');
    
    // Enqueue main stylesheet
    wp_enqueue_style('esp-style', get_stylesheet_uri(), array('esp-google-fonts'), '1.0.0');
    
    // Enqueue theme JavaScript
    wp_enqueue_script('esp-theme-js', get_template_directory_uri() . '/assets/js/theme.js', array('jquery'), '1.0.0', true);
    
    // Enqueue comment reply script
    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
    
    // Localize script for AJAX
    wp_localize_script('esp-theme-js', 'esp_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('esp_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'esp_theme_scripts');

/**
 * Register Widget Areas
 */
function esp_theme_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'esp-website-theme-two'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'esp-website-theme-two'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 1', 'esp-website-theme-two'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'esp-website-theme-two'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 2', 'esp-website-theme-two'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'esp-website-theme-two'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer Widget Area 3', 'esp-website-theme-two'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'esp-website-theme-two'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'esp_theme_widgets_init');

/**
 * Custom Logo Setup
 */
function esp_theme_custom_logo_setup() {
    $defaults = array(
        'height'      => 80,
        'width'       => 80,
        'flex-height' => true,
        'flex-width'  => true,
        'header-text' => array('site-title', 'site-description'),
    );
    add_theme_support('custom-logo', $defaults);
}
add_action('after_setup_theme', 'esp_theme_custom_logo_setup');

/**
 * Excerpt Length
 */
function esp_theme_excerpt_length($length) {
    return 30;
}
add_filter('excerpt_length', 'esp_theme_excerpt_length');

/**
 * Excerpt More
 */
function esp_theme_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'esp_theme_excerpt_more');

/**
 * Custom Post Types
 */
function esp_theme_custom_post_types() {
    // Portfolio Post Type
    register_post_type('portfolio', array(
        'labels' => array(
            'name' => __('Portfolio', 'esp-website-theme-two'),
            'singular_name' => __('Portfolio Item', 'esp-website-theme-two'),
            'add_new' => __('Add New', 'esp-website-theme-two'),
            'add_new_item' => __('Add New Portfolio Item', 'esp-website-theme-two'),
            'edit_item' => __('Edit Portfolio Item', 'esp-website-theme-two'),
            'new_item' => __('New Portfolio Item', 'esp-website-theme-two'),
            'view_item' => __('View Portfolio Item', 'esp-website-theme-two'),
            'search_items' => __('Search Portfolio', 'esp-website-theme-two'),
            'not_found' => __('No portfolio items found', 'esp-website-theme-two'),
            'not_found_in_trash' => __('No portfolio items found in trash', 'esp-website-theme-two'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-portfolio',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'portfolio'),
    ));
    
    // Testimonials Post Type
    register_post_type('testimonials', array(
        'labels' => array(
            'name' => __('Testimonials', 'esp-website-theme-two'),
            'singular_name' => __('Testimonial', 'esp-website-theme-two'),
            'add_new' => __('Add New', 'esp-website-theme-two'),
            'add_new_item' => __('Add New Testimonial', 'esp-website-theme-two'),
            'edit_item' => __('Edit Testimonial', 'esp-website-theme-two'),
            'new_item' => __('New Testimonial', 'esp-website-theme-two'),
            'view_item' => __('View Testimonial', 'esp-website-theme-two'),
            'search_items' => __('Search Testimonials', 'esp-website-theme-two'),
            'not_found' => __('No testimonials found', 'esp-website-theme-two'),
            'not_found_in_trash' => __('No testimonials found in trash', 'esp-website-theme-two'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-format-quote',
        'supports' => array('title', 'editor', 'thumbnail'),
        'rewrite' => array('slug' => 'testimonials'),
    ));
}
add_action('init', 'esp_theme_custom_post_types');

/**
 * Add Custom Image Sizes
 */
function esp_theme_image_sizes() {
    add_image_size('esp-featured', 800, 400, true);
    add_image_size('esp-portfolio', 600, 400, true);
    add_image_size('esp-thumbnail', 300, 200, true);
}
add_action('after_setup_theme', 'esp_theme_image_sizes');

/**
 * Customizer Settings
 */
function esp_theme_customize_register($wp_customize) {
    // Hero Section
    $wp_customize->add_section('esp_hero_section', array(
        'title' => __('Hero Section', 'esp-website-theme-two'),
        'priority' => 30,
    ));

    $wp_customize->add_setting('esp_hero_title', array(
        'default' => __('Professional Excellence', 'esp-website-theme-two'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('esp_hero_title', array(
        'label' => __('Hero Title', 'esp-website-theme-two'),
        'section' => 'esp_hero_section',
        'type' => 'text',
    ));

    $wp_customize->add_setting('esp_hero_subtitle', array(
        'default' => __('Crafting innovative solutions with traditional values', 'esp-website-theme-two'),
        'sanitize_callback' => 'sanitize_textarea_field',
    ));

    $wp_customize->add_control('esp_hero_subtitle', array(
        'label' => __('Hero Subtitle', 'esp-website-theme-two'),
        'section' => 'esp_hero_section',
        'type' => 'textarea',
    ));

    $wp_customize->add_setting('esp_hero_cta_text', array(
        'default' => __('Get Started', 'esp-website-theme-two'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('esp_hero_cta_text', array(
        'label' => __('Hero CTA Button Text', 'esp-website-theme-two'),
        'section' => 'esp_hero_section',
        'type' => 'text',
    ));

    $wp_customize->add_setting('esp_hero_cta_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('esp_hero_cta_url', array(
        'label' => __('Hero CTA Button URL', 'esp-website-theme-two'),
        'section' => 'esp_hero_section',
        'type' => 'url',
    ));

    // Header Section
    $wp_customize->add_section('esp_header_section', array(
        'title' => __('Header Settings', 'esp-website-theme-two'),
        'priority' => 25,
    ));

    $wp_customize->add_setting('esp_header_cta_text', array(
        'default' => __('Contact Us', 'esp-website-theme-two'),
        'sanitize_callback' => 'sanitize_text_field',
    ));

    $wp_customize->add_control('esp_header_cta_text', array(
        'label' => __('Header CTA Button Text', 'esp-website-theme-two'),
        'section' => 'esp_header_section',
        'type' => 'text',
    ));

    $wp_customize->add_setting('esp_header_cta_url', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));

    $wp_customize->add_control('esp_header_cta_url', array(
        'label' => __('Header CTA Button URL', 'esp-website-theme-two'),
        'section' => 'esp_header_section',
        'type' => 'url',
    ));

    // Social Media Section
    $wp_customize->add_section('esp_social_section', array(
        'title' => __('Social Media Links', 'esp-website-theme-two'),
        'priority' => 35,
    ));

    $social_networks = array(
        'facebook' => __('Facebook URL', 'esp-website-theme-two'),
        'twitter' => __('Twitter URL', 'esp-website-theme-two'),
        'instagram' => __('Instagram URL', 'esp-website-theme-two'),
        'linkedin' => __('LinkedIn URL', 'esp-website-theme-two'),
        'youtube' => __('YouTube URL', 'esp-website-theme-two'),
    );

    foreach ($social_networks as $network => $label) {
        $wp_customize->add_setting('esp_social_' . $network, array(
            'default' => '',
            'sanitize_callback' => 'esc_url_raw',
        ));

        $wp_customize->add_control('esp_social_' . $network, array(
            'label' => $label,
            'section' => 'esp_social_section',
            'type' => 'url',
        ));
    }

    // Colors Section
    $wp_customize->add_section('esp_colors_section', array(
        'title' => __('Theme Colors', 'esp-website-theme-two'),
        'priority' => 40,
    ));

    $wp_customize->add_setting('esp_primary_color', array(
        'default' => '#8B2635',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'esp_primary_color', array(
        'label' => __('Primary Color', 'esp-website-theme-two'),
        'section' => 'esp_colors_section',
    )));

    $wp_customize->add_setting('esp_accent_color', array(
        'default' => '#D4AF37',
        'sanitize_callback' => 'sanitize_hex_color',
    ));

    $wp_customize->add_control(new WP_Customize_Color_Control($wp_customize, 'esp_accent_color', array(
        'label' => __('Accent Color', 'esp-website-theme-two'),
        'section' => 'esp_colors_section',
    )));
}
add_action('customize_register', 'esp_theme_customize_register');

/**
 * Theme Version
 */
function esp_theme_version() {
    return '1.0.0';
}

/**
 * Custom CSS for Customizer Colors
 */
function esp_theme_customizer_css() {
    $primary_color = get_theme_mod('esp_primary_color', '#8B2635');
    $accent_color = get_theme_mod('esp_accent_color', '#D4AF37');

    if ($primary_color !== '#8B2635' || $accent_color !== '#D4AF37') {
        ?>
        <style type="text/css">
            :root {
                --primary-red: <?php echo esc_attr($primary_color); ?>;
                --accent-yellow: <?php echo esc_attr($accent_color); ?>;
            }
        </style>
        <?php
    }
}
add_action('wp_head', 'esp_theme_customizer_css');

/**
 * Add Custom Body Classes
 */
function esp_theme_body_classes($classes) {
    // Add class for pages with featured images
    if (is_singular() && has_post_thumbnail()) {
        $classes[] = 'has-featured-image';
    }

    // Add class for pages without sidebar
    if (is_page_template('page-full-width.php')) {
        $classes[] = 'full-width';
    }

    // Add class for mobile detection
    if (wp_is_mobile()) {
        $classes[] = 'mobile-device';
    }

    return $classes;
}
add_filter('body_class', 'esp_theme_body_classes');

/**
 * Custom Post Views Counter
 */
function esp_theme_set_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if ($count == '') {
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    } else {
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}

function esp_theme_track_post_views($post_id) {
    if (!is_single()) return;
    if (empty($post_id)) {
        global $post;
        $post_id = $post->ID;
    }
    esp_theme_set_post_views($post_id);
}
add_action('wp_head', 'esp_theme_track_post_views');

/**
 * Get Post Views Count
 */
function esp_theme_get_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if ($count == '') {
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0 View";
    }
    return $count . ' Views';
}

/**
 * Estimated Reading Time
 */
function esp_theme_reading_time($post_id = null) {
    if (!$post_id) {
        global $post;
        $post_id = $post->ID;
    }

    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute

    if ($reading_time == 1) {
        return '1 min read';
    } else {
        return $reading_time . ' min read';
    }
}

/**
 * Custom Pagination
 */
function esp_theme_pagination() {
    global $wp_query;

    $big = 999999999;

    echo paginate_links(array(
        'base' => str_replace($big, '%#%', esc_url(get_pagenum_link($big))),
        'format' => '?paged=%#%',
        'current' => max(1, get_query_var('paged')),
        'total' => $wp_query->max_num_pages,
        'prev_text' => '<i class="material-icons">chevron_left</i>',
        'next_text' => '<i class="material-icons">chevron_right</i>',
        'type' => 'list',
        'end_size' => 3,
        'mid_size' => 3
    ));
}

/**
 * Custom Breadcrumbs
 */
function esp_theme_breadcrumbs() {
    $delimiter = '<i class="material-icons">chevron_right</i>';
    $home = __('Home', 'esp-website-theme-two');
    $before = '<span class="current">';
    $after = '</span>';

    if (!is_home() && !is_front_page() || is_paged()) {
        echo '<nav class="breadcrumbs">';

        global $post;
        $homeLink = home_url();
        echo '<a href="' . $homeLink . '">' . $home . '</a> ' . $delimiter . ' ';

        if (is_category()) {
            global $wp_query;
            $cat_obj = $wp_query->get_queried_object();
            $thisCat = $cat_obj->term_id;
            $thisCat = get_category($thisCat);
            $parentCat = get_category($thisCat->parent);
            if ($thisCat->parent != 0) echo(get_category_parents($parentCat, TRUE, ' ' . $delimiter . ' '));
            echo $before . single_cat_title('', false) . $after;
        } elseif (is_day()) {
            echo '<a href="' . get_year_link(get_the_time('Y')) . '">' . get_the_time('Y') . '</a> ' . $delimiter . ' ';
            echo '<a href="' . get_month_link(get_the_time('Y'),get_the_time('m')) . '">' . get_the_time('F') . '</a> ' . $delimiter . ' ';
            echo $before . get_the_time('d') . $after;
        } elseif (is_month()) {
            echo '<a href="' . get_year_link(get_the_time('Y')) . '">' . get_the_time('Y') . '</a> ' . $delimiter . ' ';
            echo $before . get_the_time('F') . $after;
        } elseif (is_year()) {
            echo $before . get_the_time('Y') . $after;
        } elseif (is_single() && !is_attachment()) {
            if (get_post_type() != 'post') {
                $post_type = get_post_type_object(get_post_type());
                $slug = $post_type->rewrite;
                echo '<a href="' . $homeLink . '/' . $slug['slug'] . '/">' . $post_type->labels->singular_name . '</a> ' . $delimiter . ' ';
                echo $before . get_the_title() . $after;
            } else {
                $cat = get_the_category(); $cat = $cat[0];
                echo get_category_parents($cat, TRUE, ' ' . $delimiter . ' ');
                echo $before . get_the_title() . $after;
            }
        } elseif (!is_single() && !is_page() && get_post_type() != 'post') {
            $post_type = get_post_type_object(get_post_type());
            echo $before . $post_type->labels->singular_name . $after;
        } elseif (is_attachment()) {
            $parent = get_post($post->post_parent);
            $cat = get_the_category($parent->ID); $cat = $cat[0];
            echo get_category_parents($cat, TRUE, ' ' . $delimiter . ' ');
            echo '<a href="' . get_permalink($parent) . '">' . $parent->post_title . '</a> ' . $delimiter . ' ';
            echo $before . get_the_title() . $after;
        } elseif (is_page() && !$post->post_parent) {
            echo $before . get_the_title() . $after;
        } elseif (is_page() && $post->post_parent) {
            $parent_id  = $post->post_parent;
            $breadcrumbs = array();
            while ($parent_id) {
                $page = get_page($parent_id);
                $breadcrumbs[] = '<a href="' . get_permalink($page->ID) . '">' . get_the_title($page->ID) . '</a>';
                $parent_id  = $page->post_parent;
            }
            $breadcrumbs = array_reverse($breadcrumbs);
            foreach ($breadcrumbs as $crumb) echo $crumb . ' ' . $delimiter . ' ';
            echo $before . get_the_title() . $after;
        } elseif (is_search()) {
            echo $before . __('Search results for "', 'esp-website-theme-two') . get_search_query() . '"' . $after;
        } elseif (is_tag()) {
            echo $before . __('Posts tagged "', 'esp-website-theme-two') . single_tag_title('', false) . '"' . $after;
        } elseif (is_author()) {
            global $author;
            $userdata = get_userdata($author);
            echo $before . __('Articles posted by ', 'esp-website-theme-two') . $userdata->display_name . $after;
        } elseif (is_404()) {
            echo $before . __('Error 404', 'esp-website-theme-two') . $after;
        }

        if (get_query_var('paged')) {
            if (is_category() || is_day() || is_month() || is_year() || is_search() || is_tag() || is_author()) echo ' (';
            echo __('Page', 'esp-website-theme-two') . ' ' . get_query_var('paged');
            if (is_category() || is_day() || is_month() || is_year() || is_search() || is_tag() || is_author()) echo ')';
        }

        echo '</nav>';
    }
}
