<?php
/**
 * The template for displaying single portfolio items
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <?php while (have_posts()) : the_post(); ?>
                <article id="post-<?php the_ID(); ?>" <?php post_class('portfolio-single'); ?>>
                    <!-- Portfolio Header -->
                    <header class="portfolio-single-header">
                        <div class="row">
                            <div class="col-8 col-sm-12">
                                <?php the_title('<h1 class="portfolio-single-title">', '</h1>'); ?>
                                
                                <?php
                                $portfolio_categories = get_the_terms(get_the_ID(), 'portfolio_category');
                                if ($portfolio_categories && !is_wp_error($portfolio_categories)) :
                                ?>
                                    <div class="portfolio-single-categories">
                                        <?php foreach ($portfolio_categories as $category) : ?>
                                            <span class="portfolio-category"><?php echo esc_html($category->name); ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="portfolio-single-excerpt">
                                    <?php the_excerpt(); ?>
                                </div>
                            </div>
                            
                            <div class="col-4 col-sm-12">
                                <div class="portfolio-single-meta">
                                    <?php if (get_field('client_name')) : ?>
                                        <div class="portfolio-meta-item">
                                            <strong><?php _e('Client:', 'esp-website-theme-two'); ?></strong>
                                            <span><?php echo esc_html(get_field('client_name')); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (get_field('project_date')) : ?>
                                        <div class="portfolio-meta-item">
                                            <strong><?php _e('Date:', 'esp-website-theme-two'); ?></strong>
                                            <span><?php echo esc_html(get_field('project_date')); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (get_field('portfolio_technologies')) : ?>
                                        <div class="portfolio-meta-item">
                                            <strong><?php _e('Technologies:', 'esp-website-theme-two'); ?></strong>
                                            <span><?php echo esc_html(get_field('portfolio_technologies')); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if (get_field('portfolio_url')) : ?>
                                        <div class="portfolio-meta-item">
                                            <a href="<?php echo esc_url(get_field('portfolio_url')); ?>" class="btn btn-primary" target="_blank" rel="noopener">
                                                <i class="material-icons">launch</i>
                                                <?php _e('View Live Project', 'esp-website-theme-two'); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </header>

                    <!-- Portfolio Featured Image -->
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="portfolio-single-featured">
                            <?php the_post_thumbnail('full', array('class' => 'img-responsive')); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Portfolio Content -->
                    <div class="portfolio-single-content">
                        <div class="entry-content">
                            <?php the_content(); ?>
                        </div>
                    </div>

                    <!-- Portfolio Gallery -->
                    <?php if (have_rows('portfolio_gallery')) : ?>
                        <div class="portfolio-gallery">
                            <h3 class="portfolio-gallery-title"><?php _e('Project Gallery', 'esp-website-theme-two'); ?></h3>
                            <div class="portfolio-gallery-grid">
                                <?php while (have_rows('portfolio_gallery')) : the_row(); ?>
                                    <?php $image = get_sub_field('image'); ?>
                                    <?php if ($image) : ?>
                                        <div class="portfolio-gallery-item">
                                            <a href="<?php echo esc_url($image['url']); ?>" class="portfolio-gallery-link" data-lightbox="portfolio-gallery">
                                                <img src="<?php echo esc_url($image['sizes']['esp-portfolio']); ?>" alt="<?php echo esc_attr($image['alt']); ?>" class="img-responsive">
                                                <div class="portfolio-gallery-overlay">
                                                    <i class="material-icons">zoom_in</i>
                                                </div>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                <?php endwhile; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Portfolio Navigation -->
                    <nav class="portfolio-navigation">
                        <div class="portfolio-nav-links">
                            <?php
                            $prev_post = get_previous_post(true, '', 'portfolio_category');
                            $next_post = get_next_post(true, '', 'portfolio_category');
                            ?>
                            
                            <?php if ($prev_post) : ?>
                                <div class="portfolio-nav-previous">
                                    <a href="<?php echo esc_url(get_permalink($prev_post)); ?>" class="portfolio-nav-link">
                                        <div class="portfolio-nav-direction">
                                            <i class="material-icons">chevron_left</i>
                                            <?php _e('Previous Project', 'esp-website-theme-two'); ?>
                                        </div>
                                        <div class="portfolio-nav-title"><?php echo esc_html(get_the_title($prev_post)); ?></div>
                                        <?php if (has_post_thumbnail($prev_post->ID)) : ?>
                                            <div class="portfolio-nav-thumbnail">
                                                <?php echo get_the_post_thumbnail($prev_post->ID, 'esp-thumbnail'); ?>
                                            </div>
                                        <?php endif; ?>
                                    </a>
                                </div>
                            <?php endif; ?>

                            <div class="portfolio-nav-center">
                                <a href="<?php echo esc_url(get_post_type_archive_link('portfolio')); ?>" class="btn btn-outline">
                                    <i class="material-icons">grid_view</i>
                                    <?php _e('All Projects', 'esp-website-theme-two'); ?>
                                </a>
                            </div>

                            <?php if ($next_post) : ?>
                                <div class="portfolio-nav-next">
                                    <a href="<?php echo esc_url(get_permalink($next_post)); ?>" class="portfolio-nav-link">
                                        <div class="portfolio-nav-direction">
                                            <?php _e('Next Project', 'esp-website-theme-two'); ?>
                                            <i class="material-icons">chevron_right</i>
                                        </div>
                                        <div class="portfolio-nav-title"><?php echo esc_html(get_the_title($next_post)); ?></div>
                                        <?php if (has_post_thumbnail($next_post->ID)) : ?>
                                            <div class="portfolio-nav-thumbnail">
                                                <?php echo get_the_post_thumbnail($next_post->ID, 'esp-thumbnail'); ?>
                                            </div>
                                        <?php endif; ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </nav>

                    <!-- Related Projects -->
                    <?php
                    $related_projects = get_posts(array(
                        'post_type' => 'portfolio',
                        'numberposts' => 3,
                        'post__not_in' => array(get_the_ID()),
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'portfolio_category',
                                'field' => 'term_id',
                                'terms' => wp_get_post_terms(get_the_ID(), 'portfolio_category', array('fields' => 'ids')),
                            ),
                        ),
                    ));
                    
                    if ($related_projects) :
                    ?>
                        <section class="related-projects">
                            <h3 class="related-projects-title"><?php _e('Related Projects', 'esp-website-theme-two'); ?></h3>
                            <div class="related-projects-grid">
                                <?php foreach ($related_projects as $related_project) : ?>
                                    <article class="related-project">
                                        <?php if (has_post_thumbnail($related_project->ID)) : ?>
                                            <div class="related-project-thumbnail">
                                                <a href="<?php echo esc_url(get_permalink($related_project)); ?>">
                                                    <?php echo get_the_post_thumbnail($related_project->ID, 'esp-portfolio', array('class' => 'img-responsive')); ?>
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                        <div class="related-project-content">
                                            <h4 class="related-project-title">
                                                <a href="<?php echo esc_url(get_permalink($related_project)); ?>">
                                                    <?php echo esc_html(get_the_title($related_project)); ?>
                                                </a>
                                            </h4>
                                        </div>
                                    </article>
                                <?php endforeach; ?>
                            </div>
                        </section>
                    <?php endif; ?>

                </article>
            <?php endwhile; ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>
