<?php
/**
 * Template Name: Landing Page
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php while (have_posts()) : the_post(); ?>
        <!-- Hero Section -->
        <section class="landing-hero crocodile-pattern">
            <div class="wave-border-top"></div>
            <div class="container">
                <div class="landing-hero-content">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="landing-hero-image">
                            <?php the_post_thumbnail('large', array('class' => 'img-responsive')); ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="landing-hero-text">
                        <?php the_title('<h1 class="landing-hero-title">', '</h1>'); ?>
                        
                        <?php if (get_field('landing_subtitle')) : ?>
                            <p class="landing-hero-subtitle"><?php echo esc_html(get_field('landing_subtitle')); ?></p>
                        <?php endif; ?>
                        
                        <div class="landing-hero-actions">
                            <?php if (get_field('primary_cta_text') && get_field('primary_cta_url')) : ?>
                                <a href="<?php echo esc_url(get_field('primary_cta_url')); ?>" class="btn btn-primary btn-large">
                                    <?php echo esc_html(get_field('primary_cta_text')); ?>
                                    <i class="material-icons">arrow_forward</i>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (get_field('secondary_cta_text') && get_field('secondary_cta_url')) : ?>
                                <a href="<?php echo esc_url(get_field('secondary_cta_url')); ?>" class="btn btn-outline btn-large">
                                    <?php echo esc_html(get_field('secondary_cta_text')); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="wave-border-bottom"></div>
        </section>

        <!-- Features Section -->
        <?php if (have_rows('features')) : ?>
            <section class="landing-features">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title"><?php _e('Why Choose Us', 'esp-website-theme-two'); ?></h2>
                        <p class="section-subtitle"><?php _e('Discover what makes us different', 'esp-website-theme-two'); ?></p>
                    </div>
                    
                    <div class="features-grid">
                        <?php while (have_rows('features')) : the_row(); ?>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <?php if (get_sub_field('icon')) : ?>
                                        <i class="material-icons"><?php echo esc_html(get_sub_field('icon')); ?></i>
                                    <?php else : ?>
                                        <i class="material-icons">star</i>
                                    <?php endif; ?>
                                </div>
                                <h3 class="feature-title"><?php echo esc_html(get_sub_field('title')); ?></h3>
                                <p class="feature-description"><?php echo esc_html(get_sub_field('description')); ?></p>
                            </div>
                        <?php endwhile; ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <!-- Content Section -->
        <section class="landing-content">
            <div class="container">
                <div class="entry-content">
                    <?php the_content(); ?>
                </div>
            </div>
        </section>

        <!-- Testimonials Section -->
        <?php
        $testimonials = new WP_Query(array(
            'post_type' => 'testimonials',
            'posts_per_page' => 3,
            'post_status' => 'publish'
        ));
        
        if ($testimonials->have_posts()) :
        ?>
            <section class="landing-testimonials">
                <div class="container">
                    <div class="section-header">
                        <h2 class="section-title"><?php _e('What Our Clients Say', 'esp-website-theme-two'); ?></h2>
                    </div>
                    
                    <div class="testimonials-grid">
                        <?php while ($testimonials->have_posts()) : $testimonials->the_post(); ?>
                            <div class="testimonial-card">
                                <div class="testimonial-content">
                                    <div class="testimonial-quote">
                                        <i class="material-icons">format_quote</i>
                                    </div>
                                    <div class="testimonial-text">
                                        <?php the_content(); ?>
                                    </div>
                                </div>
                                <div class="testimonial-author">
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="testimonial-avatar">
                                            <?php the_post_thumbnail('thumbnail', array('class' => 'img-responsive')); ?>
                                        </div>
                                    <?php endif; ?>
                                    <div class="testimonial-info">
                                        <h4 class="testimonial-name"><?php the_title(); ?></h4>
                                        <?php if (get_field('company')) : ?>
                                            <p class="testimonial-company"><?php echo esc_html(get_field('company')); ?></p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                        <?php wp_reset_postdata(); ?>
                    </div>
                </div>
            </section>
        <?php endif; ?>

        <!-- CTA Section -->
        <section class="landing-cta crocodile-pattern">
            <div class="wave-border-top"></div>
            <div class="container">
                <div class="cta-content">
                    <h2 class="cta-title"><?php _e('Ready to Get Started?', 'esp-website-theme-two'); ?></h2>
                    <p class="cta-subtitle"><?php _e('Join thousands of satisfied customers today', 'esp-website-theme-two'); ?></p>
                    
                    <div class="cta-actions">
                        <?php if (get_field('final_cta_text') && get_field('final_cta_url')) : ?>
                            <a href="<?php echo esc_url(get_field('final_cta_url')); ?>" class="btn btn-secondary btn-large">
                                <?php echo esc_html(get_field('final_cta_text')); ?>
                                <i class="material-icons">arrow_forward</i>
                            </a>
                        <?php else : ?>
                            <a href="#contact" class="btn btn-secondary btn-large">
                                <?php _e('Contact Us Today', 'esp-website-theme-two'); ?>
                                <i class="material-icons">arrow_forward</i>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="wave-border-bottom"></div>
        </section>

    <?php endwhile; ?>
</main>

<?php get_footer(); ?>
