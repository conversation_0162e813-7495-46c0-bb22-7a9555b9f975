<?php
/**
 * The template for displaying search results pages
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <div class="row">
                <div class="col-8 col-sm-12">
                    <?php if (have_posts()) : ?>
                        <header class="page-header">
                            <h1 class="page-title">
                                <?php
                                printf(
                                    esc_html__('Search Results for: %s', 'esp-website-theme-two'),
                                    '<span>' . get_search_query() . '</span>'
                                );
                                ?>
                            </h1>
                            <div class="search-results-count">
                                <?php
                                global $wp_query;
                                printf(
                                    esc_html(_n(
                                        'Found %s result',
                                        'Found %s results',
                                        $wp_query->found_posts,
                                        'esp-website-theme-two'
                                    )),
                                    number_format_i18n($wp_query->found_posts)
                                );
                                ?>
                            </div>
                        </header>

                        <div class="posts-grid">
                            <?php while (have_posts()) : the_post(); ?>
                                <article id="post-<?php the_ID(); ?>" <?php post_class('post-card search-result'); ?>>
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="post-thumbnail">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('esp-thumbnail', array('class' => 'img-responsive')); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <div class="post-content">
                                        <header class="entry-header">
                                            <div class="entry-meta">
                                                <span class="post-type">
                                                    <i class="material-icons">
                                                        <?php echo get_post_type() === 'page' ? 'description' : 'article'; ?>
                                                    </i>
                                                    <?php echo esc_html(get_post_type_object(get_post_type())->labels->singular_name); ?>
                                                </span>
                                                <span class="posted-on">
                                                    <i class="material-icons">schedule</i>
                                                    <time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                        <?php echo esc_html(get_the_date()); ?>
                                                    </time>
                                                </span>
                                                <span class="byline">
                                                    <i class="material-icons">person</i>
                                                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                        <?php echo esc_html(get_the_author()); ?>
                                                    </a>
                                                </span>
                                            </div>

                                            <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '">', '</a></h2>'); ?>
                                        </header>

                                        <div class="entry-content">
                                            <?php the_excerpt(); ?>
                                            <a href="<?php the_permalink(); ?>" class="read-more">
                                                <?php _e('Read More', 'esp-website-theme-two'); ?>
                                                <i class="material-icons">arrow_forward</i>
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            <?php endwhile; ?>
                        </div>

                        <?php
                        the_posts_pagination(array(
                            'prev_text' => '<i class="material-icons">chevron_left</i>' . __('Previous', 'esp-website-theme-two'),
                            'next_text' => __('Next', 'esp-website-theme-two') . '<i class="material-icons">chevron_right</i>',
                            'before_page_number' => '<span class="meta-nav screen-reader-text">' . __('Page', 'esp-website-theme-two') . ' </span>',
                        ));
                        ?>

                    <?php else : ?>
                        <section class="no-results not-found">
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('Nothing found', 'esp-website-theme-two'); ?></h1>
                            </header>

                            <div class="page-content">
                                <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'esp-website-theme-two'); ?></p>
                                
                                <div class="search-again">
                                    <?php get_search_form(); ?>
                                </div>

                                <div class="search-suggestions">
                                    <h3><?php _e('Search Suggestions:', 'esp-website-theme-two'); ?></h3>
                                    <ul>
                                        <li><?php _e('Make sure all words are spelled correctly.', 'esp-website-theme-two'); ?></li>
                                        <li><?php _e('Try different keywords.', 'esp-website-theme-two'); ?></li>
                                        <li><?php _e('Try more general keywords.', 'esp-website-theme-two'); ?></li>
                                        <li><?php _e('Try fewer keywords.', 'esp-website-theme-two'); ?></li>
                                    </ul>
                                </div>

                                <div class="popular-content">
                                    <h3><?php _e('Popular Content', 'esp-website-theme-two'); ?></h3>
                                    <?php
                                    $popular_posts = get_posts(array(
                                        'numberposts' => 5,
                                        'meta_key' => 'post_views_count',
                                        'orderby' => 'meta_value_num',
                                        'order' => 'DESC',
                                    ));
                                    
                                    if ($popular_posts) :
                                    ?>
                                        <ul class="popular-posts-list">
                                            <?php foreach ($popular_posts as $popular_post) : ?>
                                                <li>
                                                    <a href="<?php echo esc_url(get_permalink($popular_post)); ?>">
                                                        <?php echo esc_html(get_the_title($popular_post)); ?>
                                                    </a>
                                                </li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php else : ?>
                                        <?php
                                        $recent_posts = get_posts(array('numberposts' => 5));
                                        if ($recent_posts) :
                                        ?>
                                            <ul class="recent-posts-list">
                                                <?php foreach ($recent_posts as $recent_post) : ?>
                                                    <li>
                                                        <a href="<?php echo esc_url(get_permalink($recent_post)); ?>">
                                                            <?php echo esc_html(get_the_title($recent_post)); ?>
                                                        </a>
                                                    </li>
                                                <?php endforeach; ?>
                                            </ul>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </section>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-4 col-sm-12">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
