<?php
/**
 * The template for displaying all pages
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <?php while (have_posts()) : the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="page-thumbnail">
                                    <?php the_post_thumbnail('large', array('class' => 'img-responsive')); ?>
                                </div>
                            <?php endif; ?>

                            <div class="page-content-wrapper">
                                <header class="entry-header">
                                    <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                                </header>

                                <div class="entry-content">
                                    <?php
                                    the_content();

                                    wp_link_pages(array(
                                        'before' => '<div class="page-links">' . __('Pages:', 'esp-website-theme-two'),
                                        'after'  => '</div>',
                                    ));
                                    ?>
                                </div>

                                <?php if (get_edit_post_link()) : ?>
                                    <footer class="entry-footer">
                                        <?php
                                        edit_post_link(
                                            sprintf(
                                                wp_kses(
                                                    __('Edit <span class="screen-reader-text">%s</span>', 'esp-website-theme-two'),
                                                    array(
                                                        'span' => array(
                                                            'class' => array(),
                                                        ),
                                                    )
                                                ),
                                                get_the_title()
                                            ),
                                            '<span class="edit-link">',
                                            '</span>'
                                        );
                                        ?>
                                    </footer>
                                <?php endif; ?>
                            </div>
                        </article>

                        <?php
                        // If comments are open or we have at least one comment, load up the comment template.
                        if (comments_open() || get_comments_number()) :
                            comments_template();
                        endif;
                        ?>

                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
