# ESP Website Theme Two

A professional WordPress theme inspired by traditional Indonesian heraldic colors featuring rich reds, golds, and greens with modern design patterns and animations.

## Theme Information

- **Theme Name:** ESP Website Theme Two
- **Author:** <PERSON><PERSON>
- **Author Website:** https://www.dakoiims.com
- **Version:** 1.0.0
- **License:** GPL v2 or later
- **Text Domain:** esp-website-theme-two

## Features

### Design & Layout
- **Responsive Design:** Mobile-first approach with full responsiveness across all devices
- **Modern Grid System:** Flexible 12-column grid system with responsive breakpoints
- **Crocodile Pattern Background:** Animated gradient patterns inspired by Indonesian heraldic imagery
- **Wave Border Effects:** Smooth SVG wave animations for section transitions
- **Material Icons:** Google Material Icons integration for consistent iconography

### Color Scheme
The theme uses a carefully crafted color palette inspired by traditional Indonesian heraldic imagery:
- **Primary Red:** #8B2635 - Deep burgundy red
- **Secondary Red:** #A53B47 - Lighter burgundy
- **Accent Yellow:** #D4AF37 - Rich gold
- **Accent Green:** #2E7D32 - Forest green
- **Dark Green:** #1B5E20 - Deep forest green
- **Light Cream:** #FFF8E7 - Warm cream background

### WordPress Features
- **Custom Post Types:** Portfolio and Testimonials
- **Custom Image Sizes:** Optimized image sizes for different content areas
- **Widget Areas:** Multiple widget areas including sidebar and footer widgets
- **Navigation Menus:** Primary and footer navigation support
- **Custom Logo:** Built-in custom logo support
- **Post Thumbnails:** Featured image support across all post types
- **Comments:** Threaded comments with custom styling
- **Search:** Enhanced search functionality with custom search form

### Page Templates
- **Default Templates:** index.php, single.php, page.php, archive.php, search.php, 404.php
- **Full Width Page:** page-full-width.php - Full width page template without sidebar
- **Landing Page:** page-landing.php - Special landing page with hero section and features
- **Portfolio Archive:** archive-portfolio.php - Portfolio grid layout with filtering
- **Single Portfolio:** single-portfolio.php - Detailed portfolio item display

### Customizer Options
- **Hero Section:** Customize hero title, subtitle, and call-to-action buttons
- **Header Settings:** Configure header CTA button text and URL
- **Social Media Links:** Add social media profile links for footer display
- **Theme Colors:** Customize primary and accent colors
- **Custom Background:** Built-in custom background support

### Performance & Accessibility
- **Optimized Loading:** Efficient CSS and JavaScript loading
- **Accessibility Ready:** WCAG 2.1 compliant with proper ARIA labels and keyboard navigation
- **SEO Friendly:** Semantic HTML structure and proper heading hierarchy
- **Print Styles:** Optimized styles for printing
- **Reduced Motion:** Respects user's motion preferences
- **High Contrast:** Support for high contrast mode

## Installation

1. Download the theme files
2. Upload to your WordPress `/wp-content/themes/` directory
3. Activate the theme through the WordPress admin panel
4. Go to Appearance > Customize to configure theme options

## File Structure

```
esp_website_theme_two/
├── style.css                 # Main stylesheet with theme header
├── index.php                 # Main template file
├── functions.php             # Theme functions and features
├── header.php                # Header template
├── footer.php                # Footer template
├── sidebar.php               # Sidebar template
├── single.php                # Single post template
├── page.php                  # Page template
├── archive.php               # Archive template
├── search.php                # Search results template
├── 404.php                   # 404 error template
├── comments.php              # Comments template
├── searchform.php            # Search form template
├── page-full-width.php       # Full width page template
├── page-landing.php          # Landing page template
├── archive-portfolio.php     # Portfolio archive template
├── single-portfolio.php      # Single portfolio template
├── assets/
│   └── js/
│       └── theme.js          # Theme JavaScript
├── dev_guides/
│   └── website_page_design_1.md  # Design guide reference
└── README.md                 # This file
```

## Customization

### Colors
The theme uses CSS custom properties (variables) for easy color customization. You can modify colors in the WordPress Customizer or by editing the CSS variables in `style.css`:

```css
:root {
    --primary-red: #8B2635;
    --secondary-red: #A53B47;
    --accent-yellow: #D4AF37;
    --accent-green: #2E7D32;
    --dark-green: #1B5E20;
    --light-cream: #FFF8E7;
}
```

### Typography
The theme uses the Poppins font family from Google Fonts. Font sizes are defined using CSS variables for consistency:

```css
:root {
    --font-primary: 'Poppins', sans-serif;
    --font-size-base: 16px;
    --font-size-large: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    --font-size-hero: 48px;
}
```

### Spacing
Consistent spacing is maintained using CSS variables:

```css
:root {
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;
}
```

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+ (limited support)

## Contributing

This theme is developed by Noland Gande. For support or customization requests, please visit https://www.dakoiims.com

## License

This theme is licensed under the GPL v2 or later.

## Changelog

### Version 1.0.0
- Initial release
- Complete theme structure with all templates
- Responsive design implementation
- Custom post types for Portfolio and Testimonials
- Customizer integration
- Accessibility features
- Performance optimizations
