<?php
/**
 * The template for displaying archive pages
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <div class="row">
                <div class="col-8 col-sm-12">
                    <?php if (have_posts()) : ?>
                        <header class="page-header">
                            <?php
                            the_archive_title('<h1 class="page-title">', '</h1>');
                            the_archive_description('<div class="archive-description">', '</div>');
                            ?>
                        </header>

                        <div class="posts-grid">
                            <?php while (have_posts()) : the_post(); ?>
                                <article id="post-<?php the_ID(); ?>" <?php post_class('post-card'); ?>>
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="post-thumbnail">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('esp-featured', array('class' => 'img-responsive')); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <div class="post-content">
                                        <header class="entry-header">
                                            <div class="entry-meta">
                                                <span class="posted-on">
                                                    <i class="material-icons">schedule</i>
                                                    <time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                        <?php echo esc_html(get_the_date()); ?>
                                                    </time>
                                                </span>
                                                <span class="byline">
                                                    <i class="material-icons">person</i>
                                                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                        <?php echo esc_html(get_the_author()); ?>
                                                    </a>
                                                </span>
                                                <?php if (has_category()) : ?>
                                                    <span class="cat-links">
                                                        <i class="material-icons">folder</i>
                                                        <?php the_category(', '); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '">', '</a></h2>'); ?>
                                        </header>

                                        <div class="entry-content">
                                            <?php the_excerpt(); ?>
                                            <a href="<?php the_permalink(); ?>" class="read-more">
                                                <?php _e('Read More', 'esp-website-theme-two'); ?>
                                                <i class="material-icons">arrow_forward</i>
                                            </a>
                                        </div>
                                    </div>
                                </article>
                            <?php endwhile; ?>
                        </div>

                        <?php
                        the_posts_pagination(array(
                            'prev_text' => '<i class="material-icons">chevron_left</i>' . __('Previous', 'esp-website-theme-two'),
                            'next_text' => __('Next', 'esp-website-theme-two') . '<i class="material-icons">chevron_right</i>',
                            'before_page_number' => '<span class="meta-nav screen-reader-text">' . __('Page', 'esp-website-theme-two') . ' </span>',
                        ));
                        ?>

                    <?php else : ?>
                        <section class="no-results not-found">
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('Nothing here', 'esp-website-theme-two'); ?></h1>
                            </header>

                            <div class="page-content">
                                <p><?php _e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'esp-website-theme-two'); ?></p>
                                <?php get_search_form(); ?>
                            </div>
                        </section>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-4 col-sm-12">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
