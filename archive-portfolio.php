<?php
/**
 * The template for displaying portfolio archive
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <!-- Portfolio Header -->
            <header class="portfolio-header">
                <h1 class="portfolio-title"><?php _e('Our Portfolio', 'esp-website-theme-two'); ?></h1>
                <p class="portfolio-description"><?php _e('Showcasing our finest work and creative solutions', 'esp-website-theme-two'); ?></p>
            </header>

            <?php if (have_posts()) : ?>
                <!-- Portfolio Filter -->
                <div class="portfolio-filters">
                    <button class="filter-btn active" data-filter="*"><?php _e('All', 'esp-website-theme-two'); ?></button>
                    <?php
                    $portfolio_categories = get_terms(array(
                        'taxonomy' => 'portfolio_category',
                        'hide_empty' => true,
                    ));
                    
                    if ($portfolio_categories && !is_wp_error($portfolio_categories)) :
                        foreach ($portfolio_categories as $category) :
                    ?>
                        <button class="filter-btn" data-filter=".<?php echo esc_attr($category->slug); ?>">
                            <?php echo esc_html($category->name); ?>
                        </button>
                    <?php 
                        endforeach;
                    endif;
                    ?>
                </div>

                <!-- Portfolio Grid -->
                <div class="portfolio-grid">
                    <?php while (have_posts()) : the_post(); ?>
                        <?php
                        $portfolio_categories = get_the_terms(get_the_ID(), 'portfolio_category');
                        $category_classes = '';
                        if ($portfolio_categories && !is_wp_error($portfolio_categories)) {
                            foreach ($portfolio_categories as $category) {
                                $category_classes .= ' ' . $category->slug;
                            }
                        }
                        ?>
                        
                        <article id="post-<?php the_ID(); ?>" <?php post_class('portfolio-item' . $category_classes); ?>>
                            <div class="portfolio-card">
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="portfolio-thumbnail">
                                        <?php the_post_thumbnail('esp-portfolio', array('class' => 'img-responsive')); ?>
                                        <div class="portfolio-overlay">
                                            <div class="portfolio-actions">
                                                <a href="<?php the_permalink(); ?>" class="portfolio-link" title="<?php the_title_attribute(); ?>">
                                                    <i class="material-icons">visibility</i>
                                                </a>
                                                <?php if (get_field('portfolio_url')) : ?>
                                                    <a href="<?php echo esc_url(get_field('portfolio_url')); ?>" class="portfolio-external" target="_blank" rel="noopener" title="<?php _e('View Project', 'esp-website-theme-two'); ?>">
                                                        <i class="material-icons">launch</i>
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="portfolio-content">
                                    <header class="portfolio-entry-header">
                                        <?php the_title('<h3 class="portfolio-entry-title"><a href="' . esc_url(get_permalink()) . '">', '</a></h3>'); ?>
                                        
                                        <?php if ($portfolio_categories && !is_wp_error($portfolio_categories)) : ?>
                                            <div class="portfolio-categories">
                                                <?php foreach ($portfolio_categories as $category) : ?>
                                                    <span class="portfolio-category"><?php echo esc_html($category->name); ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                    </header>

                                    <div class="portfolio-excerpt">
                                        <?php the_excerpt(); ?>
                                    </div>

                                    <?php if (get_field('portfolio_technologies')) : ?>
                                        <div class="portfolio-technologies">
                                            <strong><?php _e('Technologies:', 'esp-website-theme-two'); ?></strong>
                                            <span><?php echo esc_html(get_field('portfolio_technologies')); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </article>
                    <?php endwhile; ?>
                </div>

                <?php
                the_posts_pagination(array(
                    'prev_text' => '<i class="material-icons">chevron_left</i>' . __('Previous', 'esp-website-theme-two'),
                    'next_text' => __('Next', 'esp-website-theme-two') . '<i class="material-icons">chevron_right</i>',
                    'before_page_number' => '<span class="meta-nav screen-reader-text">' . __('Page', 'esp-website-theme-two') . ' </span>',
                ));
                ?>

            <?php else : ?>
                <section class="no-results not-found">
                    <header class="page-header">
                        <h1 class="page-title"><?php _e('No portfolio items found', 'esp-website-theme-two'); ?></h1>
                    </header>

                    <div class="page-content">
                        <p><?php _e('It seems we don&rsquo;t have any portfolio items to show at the moment. Please check back later.', 'esp-website-theme-two'); ?></p>
                        
                        <div class="no-portfolio-actions">
                            <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                                <i class="material-icons">home</i>
                                <?php _e('Back to Homepage', 'esp-website-theme-two'); ?>
                            </a>
                        </div>
                    </div>
                </section>
            <?php endif; ?>
        </div>
    </div>
</main>

<?php get_footer(); ?>
