/**
 * ESP Website Theme Two JavaScript
 * 
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        
        // Mobile Menu Toggle
        $('.menu-toggle').on('click', function() {
            var $this = $(this);
            var $menu = $('#primary-menu');
            var isExpanded = $this.attr('aria-expanded') === 'true';
            
            $this.attr('aria-expanded', !isExpanded);
            $menu.toggleClass('menu-open');
            $('body').toggleClass('menu-open');
        });

        // Search Toggle
        $('.search-toggle').on('click', function() {
            var $this = $(this);
            var $searchForm = $('#search-form');
            var isExpanded = $this.attr('aria-expanded') === 'true';
            
            $this.attr('aria-expanded', !isExpanded);
            $searchForm.toggleClass('search-open');
            
            if (!isExpanded) {
                $searchForm.find('.search-field').focus();
            }
        });

        // Search Close
        $('.search-close').on('click', function() {
            $('#search-form').removeClass('search-open');
            $('.search-toggle').attr('aria-expanded', 'false');
        });

        // Close search on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27) { // Escape key
                $('#search-form').removeClass('search-open');
                $('.search-toggle').attr('aria-expanded', 'false');
                $('.menu-toggle').attr('aria-expanded', 'false');
                $('#primary-menu').removeClass('menu-open');
                $('body').removeClass('menu-open');
            }
        });

        // Back to Top Button
        var $backToTop = $('#back-to-top');
        
        $(window).scroll(function() {
            if ($(this).scrollTop() > 300) {
                $backToTop.addClass('show');
            } else {
                $backToTop.removeClass('show');
            }
        });

        $backToTop.on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });

        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 600);
            }
        });

        // Feature cards animation on scroll
        if ($('.feature-card').length) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        $(entry.target).addClass('animate-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            $('.feature-card').each(function() {
                observer.observe(this);
            });
        }

        // Post cards animation on scroll
        if ($('.post-card').length) {
            var postObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        $(entry.target).addClass('animate-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            $('.post-card').each(function() {
                postObserver.observe(this);
            });
        }

        // Floating animation for logo
        $('.floating').each(function(index) {
            var $this = $(this);
            var delay = index * 1000; // 1 second delay between elements
            
            setTimeout(function() {
                $this.addClass('floating-active');
            }, delay);
        });

        // Header scroll effect
        var $header = $('.site-header');
        var lastScrollTop = 0;
        
        $(window).scroll(function() {
            var scrollTop = $(this).scrollTop();
            
            if (scrollTop > 100) {
                $header.addClass('header-scrolled');
            } else {
                $header.removeClass('header-scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                $header.addClass('header-hidden');
            } else {
                $header.removeClass('header-hidden');
            }
            
            lastScrollTop = scrollTop;
        });

        // Form enhancements
        $('input, textarea').on('focus', function() {
            $(this).parent().addClass('field-focused');
        }).on('blur', function() {
            if (!$(this).val()) {
                $(this).parent().removeClass('field-focused');
            }
        });

        // Initialize any existing form fields that have values
        $('input, textarea').each(function() {
            if ($(this).val()) {
                $(this).parent().addClass('field-focused');
            }
        });

        // Widget enhancements
        $('.widget').each(function() {
            var $widget = $(this);
            if ($widget.find('.widget-title').length) {
                $widget.addClass('has-title');
            }
        });

        // Image lazy loading fallback
        if ('loading' in HTMLImageElement.prototype) {
            $('img[data-src]').each(function() {
                $(this).attr('src', $(this).attr('data-src')).removeAttr('data-src');
            });
        }

        // Accessibility improvements
        $('.menu-item-has-children > a').on('click', function(e) {
            if ($(window).width() < 768) {
                e.preventDefault();
                $(this).next('.sub-menu').slideToggle();
                $(this).parent().toggleClass('menu-open');
            }
        });

        // Focus management for mobile menu
        $('.menu-toggle').on('click', function() {
            setTimeout(function() {
                if ($('#primary-menu').hasClass('menu-open')) {
                    $('#primary-menu a:first').focus();
                }
            }, 100);
        });

    });

    // Window load
    $(window).on('load', function() {
        // Remove loading class if it exists
        $('body').removeClass('loading');
        
        // Trigger any animations that should happen after page load
        $('.hero-section').addClass('loaded');
    });

    // Window resize
    $(window).on('resize', function() {
        // Close mobile menu on resize to desktop
        if ($(window).width() >= 768) {
            $('#primary-menu').removeClass('menu-open');
            $('.menu-toggle').attr('aria-expanded', 'false');
            $('body').removeClass('menu-open');
        }
    });

})(jQuery);
