<?php
/**
 * The template for displaying the footer
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */
?>

    <footer id="colophon" class="site-footer">
        <?php if (is_active_sidebar('footer-1') || is_active_sidebar('footer-2') || is_active_sidebar('footer-3')) : ?>
            <div class="footer-widgets">
                <div class="container">
                    <div class="row">
                        <div class="col-4 col-sm-12">
                            <?php if (is_active_sidebar('footer-1')) : ?>
                                <div class="footer-widget-area">
                                    <?php dynamic_sidebar('footer-1'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-4 col-sm-12">
                            <?php if (is_active_sidebar('footer-2')) : ?>
                                <div class="footer-widget-area">
                                    <?php dynamic_sidebar('footer-2'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-4 col-sm-12">
                            <?php if (is_active_sidebar('footer-3')) : ?>
                                <div class="footer-widget-area">
                                    <?php dynamic_sidebar('footer-3'); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="footer-bottom crocodile-pattern">
            <div class="footer-content">
                <div class="container">
                    <div class="footer-inner">
                        <div class="footer-info">
                            <div class="site-info">
                                <p class="copyright">
                                    &copy; <?php echo date('Y'); ?> 
                                    <a href="<?php echo esc_url(home_url('/')); ?>">
                                        <?php bloginfo('name'); ?>
                                    </a>
                                    <?php _e('All rights reserved.', 'esp-website-theme-two'); ?>
                                </p>
                                
                                <p class="theme-credit">
                                    <?php
                                    printf(
                                        __('Theme: %1$s by %2$s', 'esp-website-theme-two'),
                                        '<a href="https://www.dakoiims.com" rel="designer">ESP Website Theme Two</a>',
                                        '<a href="https://www.dakoiims.com" rel="designer">Noland Gande</a>'
                                    );
                                    ?>
                                </p>
                            </div>
                            
                            <?php if (has_nav_menu('footer')) : ?>
                                <nav class="footer-navigation">
                                    <?php
                                    wp_nav_menu(array(
                                        'theme_location' => 'footer',
                                        'menu_id'        => 'footer-menu',
                                        'menu_class'     => 'footer-menu',
                                        'container'      => false,
                                        'depth'          => 1,
                                    ));
                                    ?>
                                </nav>
                            <?php endif; ?>
                        </div>

                        <div class="footer-social">
                            <?php if (get_theme_mod('esp_social_facebook')) : ?>
                                <a href="<?php echo esc_url(get_theme_mod('esp_social_facebook')); ?>" class="social-link facebook" target="_blank" rel="noopener">
                                    <i class="material-icons">facebook</i>
                                    <span class="screen-reader-text"><?php _e('Facebook', 'esp-website-theme-two'); ?></span>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (get_theme_mod('esp_social_twitter')) : ?>
                                <a href="<?php echo esc_url(get_theme_mod('esp_social_twitter')); ?>" class="social-link twitter" target="_blank" rel="noopener">
                                    <i class="material-icons">twitter</i>
                                    <span class="screen-reader-text"><?php _e('Twitter', 'esp-website-theme-two'); ?></span>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (get_theme_mod('esp_social_instagram')) : ?>
                                <a href="<?php echo esc_url(get_theme_mod('esp_social_instagram')); ?>" class="social-link instagram" target="_blank" rel="noopener">
                                    <i class="material-icons">camera_alt</i>
                                    <span class="screen-reader-text"><?php _e('Instagram', 'esp-website-theme-two'); ?></span>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (get_theme_mod('esp_social_linkedin')) : ?>
                                <a href="<?php echo esc_url(get_theme_mod('esp_social_linkedin')); ?>" class="social-link linkedin" target="_blank" rel="noopener">
                                    <i class="material-icons">business</i>
                                    <span class="screen-reader-text"><?php _e('LinkedIn', 'esp-website-theme-two'); ?></span>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (get_theme_mod('esp_social_youtube')) : ?>
                                <a href="<?php echo esc_url(get_theme_mod('esp_social_youtube')); ?>" class="social-link youtube" target="_blank" rel="noopener">
                                    <i class="material-icons">play_circle_filled</i>
                                    <span class="screen-reader-text"><?php _e('YouTube', 'esp-website-theme-two'); ?></span>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back to Top Button -->
        <button id="back-to-top" class="back-to-top" aria-label="<?php _e('Back to top', 'esp-website-theme-two'); ?>">
            <i class="material-icons">keyboard_arrow_up</i>
        </button>
    </footer>
</div><!-- #page -->

<?php wp_footer(); ?>

</body>
</html>
