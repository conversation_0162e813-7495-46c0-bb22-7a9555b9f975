<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Landing Page</title>
    
    <!-- Materialize CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #8B2635;
            --secondary-red: #A53B47;
            --accent-yellow: #D4AF37;
            --accent-green: #2E7D32;
            --dark-green: #1B5E20;
            --light-cream: #FFF8E7;
            --gradient-primary: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
            --gradient-accent: linear-gradient(135deg, var(--accent-yellow), #F4D03F);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--light-cream);
            overflow-x: hidden;
        }

        /* Gradient Pattern Background */
        .crocodile-pattern {
            position: relative;
            background: linear-gradient(45deg, var(--primary-red), var(--secondary-red), var(--accent-yellow), var(--accent-green));
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            overflow: hidden;
        }

        .crocodile-pattern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                linear-gradient(135deg, transparent 25%, rgba(212, 175, 55, 0.3) 25%, rgba(212, 175, 55, 0.3) 50%, transparent 50%, transparent 75%, rgba(139, 38, 53, 0.2) 75%),
                linear-gradient(45deg, transparent 25%, rgba(46, 125, 50, 0.2) 25%, rgba(46, 125, 50, 0.2) 50%, transparent 50%, transparent 75%, rgba(165, 59, 71, 0.3) 75%);
            background-size: 60px 60px, 40px 40px;
            animation: patternMove 20s linear infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes patternMove {
            0% { transform: translate(0, 0); }
            100% { transform: translate(60px, 60px); }
        }

        /* Wavy Border Generator */
        .wave-border-top {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 50px;
            background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'><path d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23FFF8E7'></path></svg>") repeat-x;
            background-size: 1200px 120px;
            animation: wave 10s ease-in-out infinite;
        }

        .wave-border-bottom {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 50px;
            background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'><path d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23FFF8E7'></path></svg>") repeat-x;
            background-size: 1200px 120px;
            transform: rotate(180deg);
            animation: wave 12s ease-in-out infinite reverse;
        }

        @keyframes wave {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(-50px); }
        }

        /* Header Styles */
        .header {
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .header-content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(139, 38, 53, 0.3);
            transition: all 0.3s ease;
            border: 3px solid var(--accent-yellow);
        }

        .logo:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 12px 40px rgba(139, 38, 53, 0.4);
        }

        .logo i {
            font-size: 2rem;
            color: var(--primary-red);
        }

        /* Main Content */
        .main-content {
            min-height: calc(100vh - 240px);
            padding: 4rem 0;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 4rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: titleGlow 3s ease-in-out infinite alternate;
        }

        @keyframes titleGlow {
            0% { filter: drop-shadow(0 0 10px rgba(139, 38, 53, 0.3)); }
            100% { filter: drop-shadow(0 0 20px rgba(139, 38, 53, 0.6)); }
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: var(--primary-red);
            margin-bottom: 2rem;
            opacity: 0.8;
        }

        .cta-button {
            background: var(--gradient-primary) !important;
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: none;
            box-shadow: 0 8px 32px rgba(139, 38, 53, 0.3);
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(139, 38, 53, 0.4);
        }

        /* Features Section */
        .features-section {
            padding: 3rem 0;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-top: 4px solid var(--accent-yellow);
            margin-bottom: 2rem;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(139, 38, 53, 0.2);
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            box-shadow: 0 5px 20px rgba(212, 175, 55, 0.3);
        }

        .feature-icon i {
            font-size: 2rem;
            color: var(--primary-red);
        }

        /* Footer */
        .footer {
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .footer-content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .header-content {
                padding: 0 1rem;
            }
            
            .logo {
                width: 60px;
                height: 60px;
            }
            
            .logo i {
                font-size: 1.5rem;
            }
        }

        /* Floating Animation */
        .floating {
            animation: floating 3s ease-in-out infinite;
        }

        @keyframes floating {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header crocodile-pattern">
        <div class="wave-border-top"></div>
        <div class="header-content">
            <div class="logo floating">
                <i class="material-icons">business</i>
            </div>
            <div class="logo floating" style="animation-delay: 1s;">
                <i class="material-icons">star</i>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero-section">
                <h1 class="hero-title">Professional Excellence</h1>
                <p class="hero-subtitle">Crafting innovative solutions with traditional values</p>
                <a href="#features" class="btn cta-button waves-effect waves-light">
                    <i class="material-icons left">arrow_forward</i>
                    Discover More
                </a>
            </section>

            <!-- Features Section -->
            <section class="features-section" id="features">
                <div class="row">
                    <div class="col s12 m4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="material-icons">speed</i>
                            </div>
                            <h5 style="color: var(--primary-red); margin-bottom: 1rem;">Lightning Fast</h5>
                            <p style="color: #666;">Optimized performance that delivers results at unprecedented speed and efficiency.</p>
                        </div>
                    </div>
                    <div class="col s12 m4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="material-icons">security</i>
                            </div>
                            <h5 style="color: var(--primary-red); margin-bottom: 1rem;">Secure & Reliable</h5>
                            <p style="color: #666;">Enterprise-grade security measures protecting your valuable data and operations.</p>
                        </div>
                    </div>
                    <div class="col s12 m4">
                        <div class="feature-card">
                            <div class="feature-icon">
                                <i class="material-icons">trending_up</i>
                            </div>
                            <h5 style="color: var(--primary-red); margin-bottom: 1rem;">Scalable Growth</h5>
                            <p style="color: #666;">Solutions that grow with your business, adapting to your evolving needs.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Additional Content Section -->
            <section class="section">
                <div class="row">
                    <div class="col s12 l6">
                        <div class="card-panel" style="background: white; border-radius: 15px; border-left: 5px solid var(--accent-yellow);">
                            <h5 style="color: var(--primary-red);">Our Mission</h5>
                            <p style="color: #666;">We are committed to delivering exceptional value through innovative solutions, combining modern technology with time-tested principles to create lasting impact for our clients.</p>
                        </div>
                    </div>
                    <div class="col s12 l6">
                        <div class="card-panel" style="background: white; border-radius: 15px; border-left: 5px solid var(--accent-green);">
                            <h5 style="color: var(--primary-red);">Our Vision</h5>
                            <p style="color: #666;">To be the leading force in our industry, setting new standards for excellence while maintaining our core values of integrity, innovation, and customer satisfaction.</p>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer crocodile-pattern">
        <div class="wave-border-bottom"></div>
        <div class="footer-content">
            <p>&copy; 2025 Professional Landing Page. All rights reserved. | Designed with Excellence</p>
        </div>
    </footer>

    <!-- Materialize JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
    
    <script>
        // Initialize Materialize components
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize any Materialize components here if needed
            
            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add some interactive animations
            const featureCards = document.querySelectorAll('.feature-card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            featureCards.forEach((card) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>