<?php
/**
 * The template for displaying all single posts
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <div class="row">
                <div class="col-8 col-sm-12">
                    <?php while (have_posts()) : the_post(); ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('post-single'); ?>>
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail-single">
                                    <?php the_post_thumbnail('large', array('class' => 'img-responsive')); ?>
                                </div>
                            <?php endif; ?>

                            <div class="post-content-single">
                                <header class="entry-header">
                                    <div class="entry-meta">
                                        <span class="posted-on">
                                            <i class="material-icons">schedule</i>
                                            <time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                <?php echo esc_html(get_the_date()); ?>
                                            </time>
                                        </span>
                                        <span class="byline">
                                            <i class="material-icons">person</i>
                                            <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                <?php echo esc_html(get_the_author()); ?>
                                            </a>
                                        </span>
                                        <?php if (has_category()) : ?>
                                            <span class="cat-links">
                                                <i class="material-icons">folder</i>
                                                <?php the_category(', '); ?>
                                            </span>
                                        <?php endif; ?>
                                        <?php if (comments_open() || get_comments_number()) : ?>
                                            <span class="comments-link">
                                                <i class="material-icons">comment</i>
                                                <a href="<?php comments_link(); ?>">
                                                    <?php comments_number(__('No Comments', 'esp-website-theme-two'), __('1 Comment', 'esp-website-theme-two'), __('% Comments', 'esp-website-theme-two')); ?>
                                                </a>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                                </header>

                                <div class="entry-content">
                                    <?php
                                    the_content();

                                    wp_link_pages(array(
                                        'before' => '<div class="page-links">' . __('Pages:', 'esp-website-theme-two'),
                                        'after'  => '</div>',
                                    ));
                                    ?>
                                </div>

                                <?php if (has_tag()) : ?>
                                    <footer class="entry-footer">
                                        <div class="tag-links">
                                            <i class="material-icons">local_offer</i>
                                            <?php the_tags('', ', ', ''); ?>
                                        </div>
                                    </footer>
                                <?php endif; ?>
                            </div>
                        </article>

                        <!-- Post Navigation -->
                        <nav class="post-navigation">
                            <div class="nav-links">
                                <?php
                                $prev_post = get_previous_post();
                                $next_post = get_next_post();
                                ?>
                                
                                <?php if ($prev_post) : ?>
                                    <div class="nav-previous">
                                        <a href="<?php echo esc_url(get_permalink($prev_post)); ?>" class="nav-link">
                                            <div class="nav-direction">
                                                <i class="material-icons">chevron_left</i>
                                                <?php _e('Previous Post', 'esp-website-theme-two'); ?>
                                            </div>
                                            <div class="nav-title"><?php echo esc_html(get_the_title($prev_post)); ?></div>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php if ($next_post) : ?>
                                    <div class="nav-next">
                                        <a href="<?php echo esc_url(get_permalink($next_post)); ?>" class="nav-link">
                                            <div class="nav-direction">
                                                <?php _e('Next Post', 'esp-website-theme-two'); ?>
                                                <i class="material-icons">chevron_right</i>
                                            </div>
                                            <div class="nav-title"><?php echo esc_html(get_the_title($next_post)); ?></div>
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </nav>

                        <!-- Author Bio -->
                        <?php if (get_the_author_meta('description')) : ?>
                            <div class="author-bio">
                                <div class="author-avatar">
                                    <?php echo get_avatar(get_the_author_meta('ID'), 80); ?>
                                </div>
                                <div class="author-info">
                                    <h3 class="author-name">
                                        <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                            <?php echo esc_html(get_the_author()); ?>
                                        </a>
                                    </h3>
                                    <div class="author-description">
                                        <?php echo wp_kses_post(get_the_author_meta('description')); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Related Posts -->
                        <?php
                        $related_posts = get_posts(array(
                            'category__in' => wp_get_post_categories(get_the_ID()),
                            'numberposts'  => 3,
                            'post__not_in' => array(get_the_ID()),
                        ));
                        
                        if ($related_posts) :
                        ?>
                            <section class="related-posts">
                                <h3 class="related-title"><?php _e('Related Posts', 'esp-website-theme-two'); ?></h3>
                                <div class="related-posts-grid">
                                    <?php foreach ($related_posts as $related_post) : ?>
                                        <article class="related-post">
                                            <?php if (has_post_thumbnail($related_post->ID)) : ?>
                                                <div class="related-thumbnail">
                                                    <a href="<?php echo esc_url(get_permalink($related_post)); ?>">
                                                        <?php echo get_the_post_thumbnail($related_post->ID, 'esp-thumbnail', array('class' => 'img-responsive')); ?>
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                            <div class="related-content">
                                                <h4 class="related-post-title">
                                                    <a href="<?php echo esc_url(get_permalink($related_post)); ?>">
                                                        <?php echo esc_html(get_the_title($related_post)); ?>
                                                    </a>
                                                </h4>
                                                <div class="related-meta">
                                                    <time datetime="<?php echo esc_attr(get_the_date('c', $related_post)); ?>">
                                                        <?php echo esc_html(get_the_date('', $related_post)); ?>
                                                    </time>
                                                </div>
                                            </div>
                                        </article>
                                    <?php endforeach; ?>
                                </div>
                            </section>
                        <?php endif; ?>

                        <?php
                        // If comments are open or we have at least one comment, load up the comment template.
                        if (comments_open() || get_comments_number()) :
                            comments_template();
                        endif;
                        ?>

                    <?php endwhile; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-4 col-sm-12">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
