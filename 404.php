<?php
/**
 * The template for displaying 404 pages (not found)
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <div class="content-area">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <section class="error-404 not-found">
                        <div class="error-content">
                            <div class="error-icon">
                                <i class="material-icons">error_outline</i>
                            </div>
                            
                            <header class="page-header">
                                <h1 class="page-title"><?php esc_html_e('Oops! That page can&rsquo;t be found.', 'esp-website-theme-two'); ?></h1>
                            </header>

                            <div class="page-content">
                                <p><?php esc_html_e('It looks like nothing was found at this location. Maybe try one of the links below or a search?', 'esp-website-theme-two'); ?></p>

                                <div class="error-search">
                                    <?php get_search_form(); ?>
                                </div>

                                <div class="error-widgets">
                                    <div class="row">
                                        <div class="col-6 col-sm-12">
                                            <div class="widget widget_recent_entries">
                                                <h3 class="widget-title"><?php esc_html_e('Most Used Categories', 'esp-website-theme-two'); ?></h3>
                                                <ul>
                                                    <?php
                                                    wp_list_categories(array(
                                                        'orderby'    => 'count',
                                                        'order'      => 'DESC',
                                                        'show_count' => 1,
                                                        'title_li'   => '',
                                                        'number'     => 10,
                                                    ));
                                                    ?>
                                                </ul>
                                            </div>
                                        </div>

                                        <div class="col-6 col-sm-12">
                                            <?php
                                            $archive_content = '<p>' . sprintf(esc_html__('Try looking in the monthly archives. %1$s', 'esp-website-theme-two'), convert_smilies(':)')) . '</p>';
                                            the_widget('WP_Widget_Archives', 'dropdown=1', "after_title=</h3>$archive_content");
                                            ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="error-actions">
                                    <a href="<?php echo esc_url(home_url('/')); ?>" class="btn btn-primary">
                                        <i class="material-icons">home</i>
                                        <?php esc_html_e('Back to Homepage', 'esp-website-theme-two'); ?>
                                    </a>
                                    
                                    <a href="javascript:history.back()" class="btn btn-secondary">
                                        <i class="material-icons">arrow_back</i>
                                        <?php esc_html_e('Go Back', 'esp-website-theme-two'); ?>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
