/*
Theme Name: ESP Website Theme Two
Description: A professional WordPress theme inspired by the East Sepik Provincial emblem of Papua New Guinea, featuring rich burgundy, gold, and forest green colors with modern design patterns and animations.
Author: <PERSON><PERSON>
Author URI: https://www.dakoiims.com
Version: 1.0.0
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
Text Domain: esp-website-theme-two
Tags: responsive, modern, professional, business, portfolio, custom-colors, featured-images, threaded-comments, translation-ready
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
*/

/* CSS Variables - Color Scheme inspired by East Sepik Provincial emblem */
:root {
    --primary-red: #8B2635;
    --secondary-red: #A53B47;
    --accent-yellow: #B8860B;
    --accent-green: #2E7D32;
    --dark-green: #1B5E20;
    --light-cream: #FFF8E7;
    --gradient-primary: linear-gradient(135deg, var(--primary-red), var(--secondary-red));
    --gradient-accent: linear-gradient(135deg, var(--accent-yellow), #F4D03F);
    --gradient-green: linear-gradient(135deg, var(--accent-green), var(--dark-green));
    
    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-size-base: 16px;
    --font-size-small: 14px;
    --font-size-large: 18px;
    --font-size-xl: 24px;
    --font-size-xxl: 32px;
    --font-size-hero: 48px;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;
    
    /* Borders and Shadows */
    --border-radius: 8px;
    --border-radius-lg: 16px;
    --border-radius-xl: 24px;
    --shadow-sm: 0 2px 8px rgba(139, 38, 53, 0.1);
    --shadow-md: 0 4px 16px rgba(139, 38, 53, 0.15);
    --shadow-lg: 0 8px 32px rgba(139, 38, 53, 0.2);
    --shadow-xl: 0 12px 48px rgba(139, 38, 53, 0.25);
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: var(--font-size-base);
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: var(--light-cream);
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--primary-red);
}

h1 { font-size: var(--font-size-hero); }
h2 { font-size: var(--font-size-xxl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-large); }
h5 { font-size: var(--font-size-base); }
h6 { font-size: var(--font-size-small); }

p {
    margin-bottom: var(--spacing-sm);
    color: #666;
}

a {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: var(--secondary-red);
}

/* Container and Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

/* WordPress Core Classes */
.alignleft {
    float: left;
    margin: 0 var(--spacing-md) var(--spacing-md) 0;
}

.alignright {
    float: right;
    margin: 0 0 var(--spacing-md) var(--spacing-md);
}

.aligncenter {
    display: block;
    margin: 0 auto var(--spacing-md);
}

.wp-caption {
    max-width: 100%;
    margin-bottom: var(--spacing-md);
}

.wp-caption-text {
    font-size: var(--font-size-small);
    color: #666;
    text-align: center;
    padding: var(--spacing-xs) 0;
}

.screen-reader-text {
    position: absolute !important;
    clip: rect(1px, 1px, 1px, 1px);
    width: 1px !important;
    height: 1px !important;
    overflow: hidden;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-secondary {
    background: var(--gradient-accent);
    color: var(--primary-red);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
}

.btn-outline:hover {
    background: var(--primary-red);
    color: white;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Responsive Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--spacing-sm);
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-5 { flex: 0 0 41.666667%; }
.col-6 { flex: 0 0 50%; }
.col-7 { flex: 0 0 58.333333%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-10 { flex: 0 0 83.333333%; }
.col-11 { flex: 0 0 91.666667%; }
.col-12 { flex: 0 0 100%; }

/* East Sepik Provincial Pattern Background */
.crocodile-pattern {
    position: relative;
    background: linear-gradient(135deg, #8B2635 0%, #B8860B 25%, #2E7D32 50%, #CD853F 75%, #8B2635 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    overflow: hidden;
}

.crocodile-pattern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 1;
}

.crocodile-pattern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 40%, rgba(184, 134, 11, 0.5) 40%, rgba(184, 134, 11, 0.5) 60%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(46, 125, 50, 0.4) 40%, rgba(46, 125, 50, 0.4) 60%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(139, 38, 53, 0.15) 30%, transparent 30%);
    background-size: 100px 100px, 80px 80px, 50px 50px;
    animation: patternMove 30s linear infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes patternMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

/* Wave Border Effects */
.wave-border-top {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'><path d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23FFF8E7'></path></svg>") repeat-x;
    background-size: 1200px 120px;
    animation: wave 10s ease-in-out infinite;
}

.wave-border-bottom {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'><path d='M985.66,92.83C906.67,72,823.78,31,743.84,14.19c-82.26-17.34-168.06-16.33-250.45.39-57.84,11.73-114,31.07-172,41.86A600.21,600.21,0,0,1,0,27.35V120H1200V95.8C1132.19,118.92,1055.71,111.31,985.66,92.83Z' fill='%23FFF8E7'></path></svg>") repeat-x;
    background-size: 1200px 120px;
    transform: rotate(180deg);
    animation: wave 12s ease-in-out infinite reverse;
}

@keyframes wave {
    0%, 100% { transform: translateX(0px); }
    50% { transform: translateX(-50px); }
}

/* Header Styles */
.site-header {
    position: relative;
    min-height: 120px;
    z-index: 1000;
    transition: var(--transition-normal);
}

.site-header.header-scrolled {
    box-shadow: var(--shadow-lg);
}

.site-header.header-hidden {
    transform: translateY(-100%);
}

.header-content {
    position: relative;
    z-index: 10;
    padding: var(--spacing-lg) 0;
}

.header-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.site-branding {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo, .site-logo img {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    border: 3px solid var(--accent-yellow);
}

.logo:hover, .site-logo img:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-xl);
}

.logo i {
    font-size: 2rem;
    color: var(--primary-red);
}

.site-title {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.site-title a {
    color: white;
    text-decoration: none;
}

.site-description {
    margin: 0;
    font-size: var(--font-size-small);
    color: rgba(255, 255, 255, 0.8);
}

/* Navigation Styles */
.main-navigation {
    position: relative;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-base);
    cursor: pointer;
    padding: var(--spacing-sm);
    align-items: center;
    gap: var(--spacing-xs);
}

.menu-toggle-icon {
    display: flex;
    flex-direction: column;
    width: 24px;
    height: 18px;
    justify-content: space-between;
}

.menu-toggle-icon span {
    display: block;
    height: 2px;
    width: 100%;
    background: white;
    transition: var(--transition-normal);
}

.primary-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-md);
}

.primary-menu li {
    position: relative;
}

.primary-menu a {
    color: white;
    text-decoration: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
    font-weight: 500;
}

.primary-menu a:hover,
.primary-menu .current-menu-item > a {
    background: rgba(255, 255, 255, 0.2);
    color: var(--accent-yellow);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-toggle {
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-large);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: var(--transition-normal);
}

.search-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.header-cta {
    font-size: var(--font-size-small);
    padding: var(--spacing-sm) var(--spacing-md);
}

/* Search Form */
.search-form-container {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--light-cream);
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
    z-index: 999;
}

.search-form-container.search-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
}

.search-form-wrapper {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
}

.search-form {
    flex: 1;
}

.search-input-wrapper {
    position: relative;
    display: flex;
}

.search-field {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--primary-red);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-base);
    background: white;
    transition: var(--transition-normal);
}

.search-field:focus {
    outline: none;
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

.search-submit {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: var(--gradient-primary);
    border: none;
    border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    color: white;
    padding: 0 var(--spacing-lg);
    cursor: pointer;
    transition: var(--transition-normal);
}

.search-submit:hover {
    background: var(--gradient-accent);
    color: var(--primary-red);
}

.search-close {
    background: none;
    border: none;
    color: var(--primary-red);
    font-size: var(--font-size-large);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: 50%;
    transition: var(--transition-normal);
}

.search-close:hover {
    background: rgba(139, 38, 53, 0.1);
}

/* Responsive Breakpoints */
@media (max-width: 768px) {
    .col-sm-1 { flex: 0 0 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; }

    .row {
        margin: 0 -var(--spacing-xs);
    }

    .col {
        padding: 0 var(--spacing-xs);
    }

    h1 { font-size: var(--font-size-xxl); }
    h2 { font-size: var(--font-size-xl); }

    .container {
        padding: 0 var(--spacing-sm);
    }

    .header-inner {
        flex-direction: column;
        align-items: flex-start;
    }

    .menu-toggle {
        display: flex;
    }

    .primary-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: var(--primary-red);
        flex-direction: column;
        padding: var(--spacing-md);
        box-shadow: var(--shadow-lg);
    }

    .primary-menu.menu-open {
        display: flex;
    }

    .logo, .site-logo img {
        width: 60px;
        height: 60px;
    }

    .logo i {
        font-size: 1.5rem;
    }
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: var(--spacing-xxl) 0;
}

.hero-content {
    position: relative;
    z-index: 10;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--spacing-md);
    animation: titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { filter: drop-shadow(0 0 10px rgba(139, 38, 53, 0.3)); }
    100% { filter: drop-shadow(0 0 20px rgba(139, 38, 53, 0.6)); }
}

.hero-subtitle {
    font-size: 1.2rem;
    color: white;
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Content Area */
.content-area {
    padding: var(--spacing-xxl) 0;
}

.posts-grid {
    display: grid;
    gap: var(--spacing-xl);
}

.post-card {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    opacity: 0;
    transform: translateY(30px);
}

.post-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.post-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.post-thumbnail {
    position: relative;
    overflow: hidden;
}

.post-thumbnail img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.post-card:hover .post-thumbnail img {
    transform: scale(1.05);
}

.post-content {
    padding: var(--spacing-lg);
}

.entry-meta {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-small);
    color: #666;
    flex-wrap: wrap;
}

.entry-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.entry-meta i {
    font-size: 16px;
    color: var(--accent-yellow);
}

.entry-title {
    margin-bottom: var(--spacing-md);
}

.entry-title a {
    color: var(--primary-red);
    text-decoration: none;
    transition: var(--transition-normal);
}

.entry-title a:hover {
    color: var(--secondary-red);
}

.entry-content {
    line-height: 1.7;
    margin-bottom: var(--spacing-md);
}

.read-more {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--primary-red);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition-normal);
}

.read-more:hover {
    color: var(--secondary-red);
    transform: translateX(5px);
}

.entry-footer {
    border-top: 1px solid #eee;
    padding-top: var(--spacing-md);
}

.tag-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.tag-links a {
    background: var(--light-cream);
    color: var(--primary-red);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-small);
    text-decoration: none;
    transition: var(--transition-normal);
}

.tag-links a:hover {
    background: var(--primary-red);
    color: white;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

.page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: white;
    color: var(--primary-red);
    text-decoration: none;
    border-radius: var(--border-radius);
    border: 2px solid var(--primary-red);
    transition: var(--transition-normal);
}

.page-numbers:hover,
.page-numbers.current {
    background: var(--primary-red);
    color: white;
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border-top: 4px solid var(--accent-yellow);
    margin-bottom: var(--spacing-lg);
    opacity: 0;
    transform: translateY(30px);
}

.feature-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    box-shadow: var(--shadow-md);
}

.feature-icon i {
    font-size: 2rem;
    color: var(--primary-red);
}

/* Sidebar */
.sidebar {
    padding-left: var(--spacing-lg);
}

.widget {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.widget:hover {
    box-shadow: var(--shadow-md);
}

.widget-title {
    color: var(--primary-red);
    font-size: var(--font-size-large);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 2px solid var(--accent-yellow);
}

.widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.widget li {
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid #eee;
}

.widget li:last-child {
    border-bottom: none;
}

.widget a {
    color: #666;
    text-decoration: none;
    transition: var(--transition-normal);
}

.widget a:hover {
    color: var(--primary-red);
}

/* Footer */
.site-footer {
    position: relative;
}

.footer-widgets {
    background: #f8f9fa;
    padding: var(--spacing-xxl) 0;
}

.footer-widget-area {
    margin-bottom: var(--spacing-lg);
}

.footer-widget-area .widget {
    background: transparent;
    box-shadow: none;
    padding: 0;
}

.footer-widget-area .widget-title {
    color: var(--primary-red);
    border-bottom-color: var(--accent-yellow);
}

.footer-bottom {
    position: relative;
    min-height: 120px;
    color: white;
}

.footer-content {
    position: relative;
    z-index: 10;
    padding: var(--spacing-xl) 0;
}

.footer-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-info {
    flex: 1;
}

.site-info p {
    margin: 0;
    font-size: var(--font-size-small);
    opacity: 0.9;
}

.footer-navigation ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--spacing-md);
}

.footer-navigation a {
    color: white;
    text-decoration: none;
    font-size: var(--font-size-small);
    transition: var(--transition-normal);
}

.footer-navigation a:hover {
    color: var(--accent-yellow);
}

.footer-social {
    display: flex;
    gap: var(--spacing-sm);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--accent-yellow);
    color: var(--primary-red);
    transform: translateY(-2px);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-lg);
    right: var(--spacing-lg);
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* Animations */
.floating {
    animation: floating 3s ease-in-out infinite;
}

.floating-active {
    animation-play-state: running;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Page Header */
.page-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-xl) 0;
    background: var(--light-cream);
    border-radius: var(--border-radius-lg);
}

.page-title {
    color: var(--primary-red);
    margin: 0;
}

/* No Results */
.no-results {
    text-align: center;
    padding: var(--spacing-xxl);
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

/* Comments */
.comments-area {
    margin-top: var(--spacing-xxl);
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.comment-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comment {
    padding: var(--spacing-lg);
    border-bottom: 1px solid #eee;
}

.comment:last-child {
    border-bottom: none;
}

.comment-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.comment-author {
    font-weight: 600;
    color: var(--primary-red);
}

.comment-date {
    font-size: var(--font-size-small);
    color: #666;
}

/* Form Styles */
.field-focused .search-field,
.field-focused input,
.field-focused textarea {
    border-color: var(--accent-yellow);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.2);
}

/* Additional Mobile Responsive Styles */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .posts-grid {
        gap: var(--spacing-lg);
    }

    .sidebar {
        padding-left: 0;
        margin-top: var(--spacing-xl);
    }

    .footer-inner {
        flex-direction: column;
        text-align: center;
    }

    .footer-navigation ul {
        justify-content: center;
        flex-wrap: wrap;
    }

    .entry-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .search-form-wrapper {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .back-to-top {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        width: 45px;
        height: 45px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .feature-card,
    .post-card {
        margin-bottom: var(--spacing-md);
    }

    .container {
        padding: 0 var(--spacing-sm);
    }

    .header-content,
    .footer-content {
        padding: var(--spacing-md) 0;
    }
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .sidebar,
    .back-to-top,
    .search-form-container {
        display: none;
    }

    .content-area {
        padding: 0;
    }

    .post-card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    a {
        color: #000 !important;
        text-decoration: underline;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-red: #000;
        --secondary-red: #333;
        --accent-yellow: #000;
        --accent-green: #000;
        --light-cream: #fff;
    }

    .crocodile-pattern::before {
        display: none;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating {
        animation: none;
    }

    .crocodile-pattern {
        animation: none;
    }

    .wave-border-top,
    .wave-border-bottom {
        animation: none;
    }
}

/* Focus Styles for Accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus,
select:focus {
    outline: 3px solid var(--accent-yellow);
    outline-offset: 2px;
}

/* Skip Link */
.skip-link {
    position: absolute;
    left: -9999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

.skip-link:focus {
    position: fixed;
    top: 0;
    left: 0;
    width: auto;
    height: auto;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-red);
    color: white;
    text-decoration: none;
    z-index: 9999;
    border-radius: 0 0 var(--border-radius) 0;
}

/* Loading State */
.loading {
    overflow: hidden;
}

.loading .hero-section {
    opacity: 0;
    transform: translateY(20px);
}

.hero-section.loaded {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.8s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-cream);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-red);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-red);
}

/* Single Post Styles */
.post-single {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.post-thumbnail-single {
    position: relative;
    overflow: hidden;
    height: 400px;
}

.post-thumbnail-single img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.post-content-single {
    padding: var(--spacing-xl);
}

.post-content-single .entry-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-lg);
}

.post-content-single .entry-content {
    font-size: var(--font-size-large);
    line-height: 1.8;
}

.post-content-single .entry-content h2,
.post-content-single .entry-content h3,
.post-content-single .entry-content h4 {
    margin-top: var(--spacing-xl);
    margin-bottom: var(--spacing-md);
}

.post-content-single .entry-content p {
    margin-bottom: var(--spacing-md);
}

.post-content-single .entry-content blockquote {
    background: var(--light-cream);
    border-left: 4px solid var(--accent-yellow);
    padding: var(--spacing-lg);
    margin: var(--spacing-lg) 0;
    font-style: italic;
    border-radius: var(--border-radius);
}

.post-content-single .entry-content ul,
.post-content-single .entry-content ol {
    margin-bottom: var(--spacing-md);
    padding-left: var(--spacing-lg);
}

.post-content-single .entry-content li {
    margin-bottom: var(--spacing-xs);
}

/* Page Content Styles */
.page-content {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.page-thumbnail {
    position: relative;
    overflow: hidden;
    height: 300px;
}

.page-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-content-wrapper {
    padding: var(--spacing-xl);
}

.page-content-wrapper .entry-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

/* Post Navigation */
.post-navigation {
    margin: var(--spacing-xl) 0;
}

.nav-links {
    display: flex;
    gap: var(--spacing-lg);
}

.nav-previous,
.nav-next {
    flex: 1;
}

.nav-link {
    display: block;
    background: white;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.nav-link:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--accent-yellow);
    transform: translateY(-2px);
}

.nav-direction {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-small);
    color: #666;
    margin-bottom: var(--spacing-xs);
}

.nav-next .nav-direction {
    justify-content: flex-end;
}

.nav-title {
    font-weight: 600;
    color: var(--primary-red);
}

/* Author Bio */
.author-bio {
    background: var(--light-cream);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    display: flex;
    gap: var(--spacing-lg);
    align-items: flex-start;
}

.author-avatar {
    flex-shrink: 0;
}

.author-avatar img {
    border-radius: 50%;
    border: 3px solid var(--accent-yellow);
}

.author-info {
    flex: 1;
}

.author-name {
    margin-bottom: var(--spacing-sm);
}

.author-name a {
    color: var(--primary-red);
    text-decoration: none;
}

.author-description {
    color: #666;
    line-height: 1.6;
}

/* Related Posts */
.related-posts {
    margin: var(--spacing-xl) 0;
}

.related-title {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-red);
}

.related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.related-post {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.related-post:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.related-thumbnail {
    height: 150px;
    overflow: hidden;
}

.related-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.related-post:hover .related-thumbnail img {
    transform: scale(1.05);
}

.related-content {
    padding: var(--spacing-md);
}

.related-post-title {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-base);
}

.related-post-title a {
    color: var(--primary-red);
    text-decoration: none;
}

.related-meta {
    font-size: var(--font-size-small);
    color: #666;
}

/* Error 404 Styles */
.error-404 {
    text-align: center;
    padding: var(--spacing-xxl);
}

.error-content {
    max-width: 600px;
    margin: 0 auto;
}

.error-icon {
    font-size: 6rem;
    color: var(--accent-yellow);
    margin-bottom: var(--spacing-lg);
}

.error-icon i {
    font-size: inherit;
}

.error-search {
    margin: var(--spacing-xl) 0;
}

.error-widgets {
    margin: var(--spacing-xl) 0;
    text-align: left;
}

.error-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--spacing-xl);
}

/* Search Results */
.search-result {
    border-left: 4px solid var(--accent-yellow);
}

.search-results-count {
    font-size: var(--font-size-small);
    color: #666;
    margin-top: var(--spacing-sm);
}

.search-again {
    margin: var(--spacing-lg) 0;
}

.search-suggestions {
    background: var(--light-cream);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    margin: var(--spacing-lg) 0;
}

.search-suggestions ul {
    margin-top: var(--spacing-sm);
}

.popular-content {
    margin-top: var(--spacing-lg);
}

.popular-posts-list,
.recent-posts-list {
    list-style: none;
    padding: 0;
}

.popular-posts-list li,
.recent-posts-list li {
    padding: var(--spacing-xs) 0;
    border-bottom: 1px solid #eee;
}

.popular-posts-list li:last-child,
.recent-posts-list li:last-child {
    border-bottom: none;
}

/* Full Width Page Styles */
.full-width-page {
    margin-bottom: 0;
}

.page-thumbnail-hero {
    position: relative;
    height: 60vh;
    overflow: hidden;
}

.page-thumbnail-hero img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(139, 38, 53, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-hero-content {
    text-align: center;
    color: white;
}

.page-hero-title {
    font-size: var(--font-size-hero);
    color: white;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Landing Page Styles */
.landing-hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xxl) 0;
}

.landing-hero-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-xxl);
    max-width: 1000px;
    margin: 0 auto;
}

.landing-hero-image {
    flex: 1;
}

.landing-hero-text {
    flex: 1;
    color: white;
}

.landing-hero-title {
    font-size: 3.5rem;
    color: white;
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.landing-hero-subtitle {
    font-size: var(--font-size-large);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.landing-hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-large);
}

.landing-features {
    padding: var(--spacing-xxl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-large);
    color: #666;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.landing-content {
    padding: var(--spacing-xxl) 0;
    background: var(--light-cream);
}

.landing-testimonials {
    padding: var(--spacing-xxl) 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.testimonial-card {
    background: white;
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.testimonial-quote {
    text-align: center;
    margin-bottom: var(--spacing-md);
}

.testimonial-quote i {
    font-size: 3rem;
    color: var(--accent-yellow);
}

.testimonial-text {
    font-style: italic;
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.testimonial-avatar {
    flex-shrink: 0;
}

.testimonial-avatar img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-name {
    margin: 0;
    color: var(--primary-red);
}

.testimonial-company {
    margin: 0;
    font-size: var(--font-size-small);
    color: #666;
}

.landing-cta {
    padding: var(--spacing-xxl) 0;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
}

.cta-title {
    font-size: var(--font-size-xxl);
    color: white;
    margin-bottom: var(--spacing-md);
}

.cta-subtitle {
    font-size: var(--font-size-large);
    color: white;
    opacity: 0.9;
    margin-bottom: var(--spacing-xl);
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* Portfolio Styles */
.portfolio-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    padding: var(--spacing-xl) 0;
}

.portfolio-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.portfolio-description {
    font-size: var(--font-size-large);
    color: #666;
}

.portfolio-filters {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-xxl);
    flex-wrap: wrap;
}

.filter-btn {
    background: white;
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-red);
    color: white;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.portfolio-item {
    opacity: 1;
    transition: var(--transition-normal);
}

.portfolio-item.filtered-out {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.portfolio-card {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
}

.portfolio-thumbnail {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.portfolio-card:hover .portfolio-thumbnail img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(139, 38, 53, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-actions {
    display: flex;
    gap: var(--spacing-md);
}

.portfolio-link,
.portfolio-external {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: white;
    color: var(--primary-red);
    border-radius: 50%;
    text-decoration: none;
    transition: var(--transition-normal);
}

.portfolio-link:hover,
.portfolio-external:hover {
    background: var(--accent-yellow);
    color: var(--primary-red);
    transform: scale(1.1);
}

.portfolio-content {
    padding: var(--spacing-lg);
}

.portfolio-entry-title {
    margin-bottom: var(--spacing-sm);
}

.portfolio-entry-title a {
    color: var(--primary-red);
    text-decoration: none;
}

.portfolio-categories {
    margin-bottom: var(--spacing-md);
}

.portfolio-category {
    display: inline-block;
    background: var(--light-cream);
    color: var(--primary-red);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: var(--font-size-small);
    margin-right: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
}

.portfolio-excerpt {
    margin-bottom: var(--spacing-md);
    color: #666;
}

.portfolio-technologies {
    font-size: var(--font-size-small);
    color: #666;
}

.portfolio-technologies strong {
    color: var(--primary-red);
}

/* Single Portfolio Styles */
.portfolio-single {
    background: white;
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-xl);
}

.portfolio-single-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid #eee;
}

.portfolio-single-title {
    font-size: var(--font-size-xxl);
    margin-bottom: var(--spacing-md);
}

.portfolio-single-categories {
    margin-bottom: var(--spacing-md);
}

.portfolio-single-excerpt {
    font-size: var(--font-size-large);
    color: #666;
    line-height: 1.7;
}

.portfolio-single-meta {
    background: var(--light-cream);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
}

.portfolio-meta-item {
    margin-bottom: var(--spacing-md);
}

.portfolio-meta-item:last-child {
    margin-bottom: 0;
}

.portfolio-meta-item strong {
    display: block;
    color: var(--primary-red);
    margin-bottom: var(--spacing-xs);
}

.portfolio-single-featured {
    margin-bottom: var(--spacing-xl);
}

.portfolio-single-featured img {
    width: 100%;
    height: auto;
}

.portfolio-single-content {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.portfolio-single-content .entry-content {
    font-size: var(--font-size-large);
    line-height: 1.8;
}

.portfolio-gallery {
    padding: 0 var(--spacing-xl) var(--spacing-xl);
}

.portfolio-gallery-title {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-red);
}

.portfolio-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
}

.portfolio-gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius-lg);
    aspect-ratio: 4/3;
}

.portfolio-gallery-link {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
}

.portfolio-gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.portfolio-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(139, 38, 53, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-normal);
}

.portfolio-gallery-item:hover .portfolio-gallery-overlay {
    opacity: 1;
}

.portfolio-gallery-item:hover img {
    transform: scale(1.1);
}

.portfolio-gallery-overlay i {
    color: white;
    font-size: 2rem;
}

.portfolio-navigation {
    margin: var(--spacing-xl) 0;
    padding: var(--spacing-xl);
    background: var(--light-cream);
}

.portfolio-nav-links {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: var(--spacing-lg);
    align-items: center;
}

.portfolio-nav-previous {
    justify-self: start;
}

.portfolio-nav-center {
    justify-self: center;
}

.portfolio-nav-next {
    justify-self: end;
}

.portfolio-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    background: white;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    max-width: 300px;
}

.portfolio-nav-link:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.portfolio-nav-direction {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-small);
    color: #666;
}

.portfolio-nav-title {
    font-weight: 600;
    color: var(--primary-red);
    margin: var(--spacing-xs) 0;
}

.portfolio-nav-thumbnail {
    flex-shrink: 0;
}

.portfolio-nav-thumbnail img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.related-projects {
    padding: var(--spacing-xl);
    border-top: 1px solid #eee;
}

.related-projects-title {
    text-align: center;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-red);
}

.related-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.related-project {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.related-project:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.related-project-thumbnail {
    height: 150px;
    overflow: hidden;
}

.related-project-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.related-project:hover .related-project-thumbnail img {
    transform: scale(1.05);
}

.related-project-content {
    padding: var(--spacing-md);
}

.related-project-title {
    margin: 0;
    font-size: var(--font-size-base);
}

.related-project-title a {
    color: var(--primary-red);
    text-decoration: none;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .landing-hero-content {
        flex-direction: column;
        text-align: center;
    }

    .landing-hero-title {
        font-size: 2.5rem;
    }

    .page-hero-title {
        font-size: 2.5rem;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-nav-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .portfolio-nav-center {
        order: -1;
        justify-self: center;
    }

    .portfolio-nav-link {
        max-width: none;
    }

    .features-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .portfolio-single-header .row {
        flex-direction: column;
    }

    .portfolio-single-meta {
        margin-top: var(--spacing-lg);
    }
}
