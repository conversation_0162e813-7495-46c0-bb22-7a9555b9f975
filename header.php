<?php
/**
 * The header for our theme
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#main"><?php _e('Skip to content', 'esp-website-theme-two'); ?></a>

    <header id="masthead" class="site-header crocodile-pattern">
        <div class="wave-border-top"></div>
        
        <div class="header-content">
            <div class="container">
                <div class="header-inner">
                    <!-- Site Branding -->
                    <div class="site-branding">
                        <?php if (has_custom_logo()) : ?>
                            <div class="site-logo">
                                <?php the_custom_logo(); ?>
                            </div>
                        <?php else : ?>
                            <div class="logo floating">
                                <i class="material-icons">business</i>
                            </div>
                        <?php endif; ?>
                        
                        <div class="site-identity">
                            <?php if (is_front_page() && is_home()) : ?>
                                <h1 class="site-title">
                                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                        <?php bloginfo('name'); ?>
                                    </a>
                                </h1>
                            <?php else : ?>
                                <p class="site-title">
                                    <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                                        <?php bloginfo('name'); ?>
                                    </a>
                                </p>
                            <?php endif; ?>
                            
                            <?php
                            $description = get_bloginfo('description', 'display');
                            if ($description || is_customize_preview()) :
                            ?>
                                <p class="site-description"><?php echo $description; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Primary Navigation -->
                    <nav id="site-navigation" class="main-navigation">
                        <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                            <span class="menu-toggle-icon">
                                <span></span>
                                <span></span>
                                <span></span>
                            </span>
                            <span class="menu-toggle-text"><?php _e('Menu', 'esp-website-theme-two'); ?></span>
                        </button>
                        
                        <?php
                        wp_nav_menu(array(
                            'theme_location' => 'primary',
                            'menu_id'        => 'primary-menu',
                            'menu_class'     => 'primary-menu',
                            'container'      => false,
                            'fallback_cb'    => 'esp_theme_fallback_menu',
                        ));
                        ?>
                    </nav>

                    <!-- Header Actions -->
                    <div class="header-actions">
                        <!-- Search Toggle -->
                        <button class="search-toggle" aria-controls="search-form" aria-expanded="false">
                            <i class="material-icons">search</i>
                            <span class="screen-reader-text"><?php _e('Search', 'esp-website-theme-two'); ?></span>
                        </button>
                        
                        <!-- Additional Action Button -->
                        <?php if (get_theme_mod('esp_header_cta_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('esp_header_cta_url')); ?>" class="btn btn-secondary header-cta">
                                <?php echo esc_html(get_theme_mod('esp_header_cta_text', __('Get Started', 'esp-website-theme-two'))); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Form -->
        <div class="search-form-container" id="search-form">
            <div class="container">
                <div class="search-form-wrapper">
                    <?php get_search_form(); ?>
                    <button class="search-close">
                        <i class="material-icons">close</i>
                        <span class="screen-reader-text"><?php _e('Close search', 'esp-website-theme-two'); ?></span>
                    </button>
                </div>
            </div>
        </div>
        
        <div class="wave-border-bottom"></div>
    </header>

<?php
/**
 * Fallback menu function
 */
function esp_theme_fallback_menu() {
    echo '<ul id="primary-menu" class="primary-menu">';
    echo '<li><a href="' . esc_url(home_url('/')) . '">' . __('Home', 'esp-website-theme-two') . '</a></li>';
    
    // Add pages to menu
    $pages = get_pages(array('sort_column' => 'menu_order'));
    foreach ($pages as $page) {
        echo '<li><a href="' . esc_url(get_permalink($page->ID)) . '">' . esc_html($page->post_title) . '</a></li>';
    }
    
    echo '</ul>';
}
?>
