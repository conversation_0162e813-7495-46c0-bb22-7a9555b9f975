<?php
/**
 * The main template file
 *
 * @package ESP_Website_Theme_Two
 * <AUTHOR>
 * @link https://www.dakoiims.com
 * @since 1.0.0
 */

get_header(); ?>

<main id="main" class="site-main">
    <?php if (is_home() && !is_paged()) : ?>
        <!-- Hero Section for Homepage -->
        <section class="hero-section crocodile-pattern">
            <div class="wave-border-top"></div>
            <div class="container">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <?php echo esc_html(get_theme_mod('esp_hero_title', __('Professional Excellence', 'esp-website-theme-two'))); ?>
                    </h1>
                    <p class="hero-subtitle">
                        <?php echo esc_html(get_theme_mod('esp_hero_subtitle', __('Crafting innovative solutions with traditional values', 'esp-website-theme-two'))); ?>
                    </p>
                    <div class="hero-actions">
                        <a href="#content" class="btn btn-primary">
                            <i class="material-icons">arrow_downward</i>
                            <?php _e('Discover More', 'esp-website-theme-two'); ?>
                        </a>
                        <?php if (get_theme_mod('esp_hero_cta_url')) : ?>
                            <a href="<?php echo esc_url(get_theme_mod('esp_hero_cta_url')); ?>" class="btn btn-secondary">
                                <?php echo esc_html(get_theme_mod('esp_hero_cta_text', __('Get Started', 'esp-website-theme-two'))); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <div class="wave-border-bottom"></div>
        </section>
    <?php endif; ?>

    <!-- Main Content Area -->
    <div class="content-area" id="content">
        <div class="container">
            <div class="row">
                <div class="col-8 col-sm-12">
                    <?php if (have_posts()) : ?>
                        <?php if (is_home() && !is_front_page()) : ?>
                            <header class="page-header">
                                <h1 class="page-title"><?php single_post_title(); ?></h1>
                            </header>
                        <?php endif; ?>

                        <div class="posts-grid">
                            <?php while (have_posts()) : the_post(); ?>
                                <article id="post-<?php the_ID(); ?>" <?php post_class('post-card'); ?>>
                                    <?php if (has_post_thumbnail()) : ?>
                                        <div class="post-thumbnail">
                                            <a href="<?php the_permalink(); ?>">
                                                <?php the_post_thumbnail('esp-featured', array('class' => 'img-responsive')); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>

                                    <div class="post-content">
                                        <header class="entry-header">
                                            <div class="entry-meta">
                                                <span class="posted-on">
                                                    <i class="material-icons">schedule</i>
                                                    <time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                                        <?php echo esc_html(get_the_date()); ?>
                                                    </time>
                                                </span>
                                                <span class="byline">
                                                    <i class="material-icons">person</i>
                                                    <a href="<?php echo esc_url(get_author_posts_url(get_the_author_meta('ID'))); ?>">
                                                        <?php echo esc_html(get_the_author()); ?>
                                                    </a>
                                                </span>
                                                <?php if (has_category()) : ?>
                                                    <span class="cat-links">
                                                        <i class="material-icons">folder</i>
                                                        <?php the_category(', '); ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <?php if (is_singular()) : ?>
                                                <?php the_title('<h1 class="entry-title">', '</h1>'); ?>
                                            <?php else : ?>
                                                <?php the_title('<h2 class="entry-title"><a href="' . esc_url(get_permalink()) . '">', '</a></h2>'); ?>
                                            <?php endif; ?>
                                        </header>

                                        <div class="entry-content">
                                            <?php if (is_singular()) : ?>
                                                <?php the_content(); ?>
                                                <?php
                                                wp_link_pages(array(
                                                    'before' => '<div class="page-links">' . __('Pages:', 'esp-website-theme-two'),
                                                    'after'  => '</div>',
                                                ));
                                                ?>
                                            <?php else : ?>
                                                <?php the_excerpt(); ?>
                                                <a href="<?php the_permalink(); ?>" class="read-more">
                                                    <?php _e('Read More', 'esp-website-theme-two'); ?>
                                                    <i class="material-icons">arrow_forward</i>
                                                </a>
                                            <?php endif; ?>
                                        </div>

                                        <?php if (is_singular() && has_tag()) : ?>
                                            <footer class="entry-footer">
                                                <div class="tag-links">
                                                    <i class="material-icons">local_offer</i>
                                                    <?php the_tags('', ', ', ''); ?>
                                                </div>
                                            </footer>
                                        <?php endif; ?>
                                    </div>
                                </article>
                            <?php endwhile; ?>
                        </div>

                        <?php
                        // Pagination
                        the_posts_pagination(array(
                            'prev_text' => '<i class="material-icons">chevron_left</i>' . __('Previous', 'esp-website-theme-two'),
                            'next_text' => __('Next', 'esp-website-theme-two') . '<i class="material-icons">chevron_right</i>',
                            'before_page_number' => '<span class="meta-nav screen-reader-text">' . __('Page', 'esp-website-theme-two') . ' </span>',
                        ));
                        ?>

                    <?php else : ?>
                        <section class="no-results not-found">
                            <header class="page-header">
                                <h1 class="page-title"><?php _e('Nothing here', 'esp-website-theme-two'); ?></h1>
                            </header>

                            <div class="page-content">
                                <?php if (is_home() && current_user_can('publish_posts')) : ?>
                                    <p>
                                        <?php
                                        printf(
                                            wp_kses(
                                                __('Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'esp-website-theme-two'),
                                                array(
                                                    'a' => array(
                                                        'href' => array(),
                                                    ),
                                                )
                                            ),
                                            esc_url(admin_url('post-new.php'))
                                        );
                                        ?>
                                    </p>
                                <?php elseif (is_search()) : ?>
                                    <p><?php _e('Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'esp-website-theme-two'); ?></p>
                                    <?php get_search_form(); ?>
                                <?php else : ?>
                                    <p><?php _e('It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'esp-website-theme-two'); ?></p>
                                    <?php get_search_form(); ?>
                                <?php endif; ?>
                            </div>
                        </section>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-4 col-sm-12">
                    <?php get_sidebar(); ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
